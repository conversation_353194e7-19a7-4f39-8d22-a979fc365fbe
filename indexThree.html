<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevCraft Studios - Web Development Excellence</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #000000 0%, #2d1b4e 50%, #000000 100%);
            color: #ffffff;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 20px 50px;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(138, 43, 226, 0.3);
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #8a2be2, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            color: #ffffff;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: #8a2be2;
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -5px;
            left: 0;
            background: linear-gradient(45deg, #8a2be2, #ffffff);
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        /* Hero Section with 3D Background */
        .hero {
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        #three-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            padding: 0 20px;
        }

        .hero-title {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffffff, #8a2be2, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(138, 43, 226, 0.5); }
            to { text-shadow: 0 0 30px rgba(138, 43, 226, 0.8); }
        }

        .hero-subtitle {
            font-size: 1.5rem;
            margin-bottom: 30px;
            opacity: 0.9;
            line-height: 1.6;
        }

        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 15px 35px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            border-radius: 50px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(45deg, #8a2be2, #6a1b9a);
            color: #ffffff;
            box-shadow: 0 10px 30px rgba(138, 43, 226, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(138, 43, 226, 0.6);
        }

        .btn-secondary {
            background: transparent;
            color: #ffffff;
            border: 2px solid #8a2be2;
        }

        .btn-secondary:hover {
            background: #8a2be2;
            transform: translateY(-3px);
        }

        /* Services Section */
        .services {
            padding: 100px 50px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffffff, #8a2be2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.2rem;
            margin-bottom: 60px;
            opacity: 0.8;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }

        .service-card {
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid rgba(138, 43, 226, 0.3);
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .service-card:hover {
            transform: translateY(-10px);
            background: rgba(138, 43, 226, 0.2);
            box-shadow: 0 20px 40px rgba(138, 43, 226, 0.3);
        }

        .service-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(45deg, #8a2be2, #6a1b9a);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .service-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #8a2be2;
        }

        .service-description {
            opacity: 0.9;
            line-height: 1.6;
        }

        /* Quality Section */
        .quality {
            padding: 100px 50px;
            background: rgba(138, 43, 226, 0.05);
            border-top: 1px solid rgba(138, 43, 226, 0.2);
            border-bottom: 1px solid rgba(138, 43, 226, 0.2);
        }

        .quality-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 60px;
            align-items: center;
        }

        .quality-text h2 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffffff, #8a2be2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .quality-text p {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .quality-features {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .feature {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .feature-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #8a2be2, #6a1b9a);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        /* Stats Section */
        .stats {
            padding: 80px 50px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            text-align: center;
        }

        .stat-item {
            padding: 30px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #8a2be2;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header {
                padding: 15px 20px;
            }

            .nav-links {
                display: none;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.2rem;
            }

            .services {
                padding: 60px 20px;
            }

            .quality-content {
                grid-template-columns: 1fr;
                gap: 40px;
            }

            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }

        /* Scroll indicator */
        .scroll-indicator {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 2;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
            40% { transform: translateX(-50%) translateY(-10px); }
            60% { transform: translateX(-50%) translateY(-5px); }
        }

        .scroll-arrow {
            width: 30px;
            height: 30px;
            border: 2px solid #8a2be2;
            border-top: none;
            border-right: none;
            transform: rotate(-45deg);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <div class="logo">DevCraft Studios</div>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#services">Services</a></li>
                <li><a href="#work">Our Work</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <canvas id="three-canvas"></canvas>
        <div class="hero-content">
            <h1 class="hero-title">Crafting Digital Excellence</h1>
            <p class="hero-subtitle">We transform your vision into stunning web experiences using cutting-edge technology and innovative design</p>
            <div class="cta-buttons">
                <a href="#services" class="btn btn-primary">Our Services</a>
                <a href="#work" class="btn btn-secondary">View Our Work</a>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-arrow"></div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <h2 class="section-title">Our Services</h2>
        <p class="section-subtitle">We offer comprehensive web development solutions tailored to your business needs</p>
        
        <div class="services-grid">
            <div class="service-card">
                <div class="service-icon">🌐</div>
                <h3 class="service-title">Web Development</h3>
                <p class="service-description">Custom websites and web applications built with modern technologies like React, Vue.js, and Node.js for optimal performance and user experience.</p>
            </div>
            
            <div class="service-card">
                <div class="service-icon">📱</div>
                <h3 class="service-title">Responsive Design</h3>
                <p class="service-description">Mobile-first designs that look stunning and function perfectly across all devices and screen sizes, ensuring maximum reach and engagement.</p>
            </div>
            
            <div class="service-card">
                <div class="service-icon">🚀</div>
                <h3 class="service-title">Performance Optimization</h3>
                <p class="service-description">Lightning-fast loading speeds and optimized performance through advanced techniques, CDN integration, and code optimization.</p>
            </div>
            
            <div class="service-card">
                <div class="service-icon">🛡️</div>
                <h3 class="service-title">Security & Maintenance</h3>
                <p class="service-description">Robust security implementations and ongoing maintenance to keep your website secure, updated, and running smoothly.</p>
            </div>
            
            <div class="service-card">
                <div class="service-icon">🎨</div>
                <h3 class="service-title">UI/UX Design</h3>
                <p class="service-description">Intuitive and engaging user interfaces designed with user experience in mind, creating memorable digital interactions.</p>
            </div>
            
            <div class="service-card">
                <div class="service-icon">⚡</div>
                <h3 class="service-title">API Integration</h3>
                <p class="service-description">Seamless integration with third-party services, payment gateways, and custom API development for enhanced functionality.</p>
            </div>
        </div>
    </section>

    <!-- Quality Section -->
    <section class="quality">
        <div class="quality-content">
            <div class="quality-text">
                <h2>Why Choose Us?</h2>
                <p>We're passionate about delivering exceptional web solutions that drive results. Our team combines technical expertise with creative vision to create digital experiences that exceed expectations.</p>
                <p>Every project is approached with meticulous attention to detail, ensuring your website not only looks amazing but performs flawlessly across all platforms.</p>
            </div>
            <div class="quality-features">
                <div class="feature">
                    <div class="feature-icon">✓</div>
                    <span>100% Custom Solutions</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">✓</div>
                    <span>Lightning Fast Performance</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">✓</div>
                    <span>SEO Optimized</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">✓</div>
                    <span>24/7 Support</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">✓</div>
                    <span>Modern Technologies</span>
                </div>
                <div class="feature">
                    <div class="feature-icon">✓</div>
                    <span>Scalable Architecture</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">150+</div>
                <div class="stat-label">Projects Completed</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">50+</div>
                <div class="stat-label">Happy Clients</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">5+</div>
                <div class="stat-label">Years Experience</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">99%</div>
                <div class="stat-label">Client Satisfaction</div>
            </div>
        </div>
    </section>

    <script>
        // Three.js 3D Background Animation
        let scene, camera, renderer, particles;

        function init3D() {
            // Scene setup
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            renderer = new THREE.WebGLRenderer({ canvas: document.getElementById('three-canvas'), alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);

            // Create particles
            const particlesGeometry = new THREE.BufferGeometry();
            const particlesCount = 1000;
            const posArray = new Float32Array(particlesCount * 3);

            for (let i = 0; i < particlesCount * 3; i++) {
                posArray[i] = (Math.random() - 0.5) * 20;
            }

            particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));

            // Create material with violet color
            const particlesMaterial = new THREE.PointsMaterial({
                size: 0.05,
                color: 0x8a2be2,
                transparent: true,
                opacity: 0.8
            });

            particles = new THREE.Points(particlesGeometry, particlesMaterial);
            scene.add(particles);

            // Create floating geometric shapes
            const geometries = [
                new THREE.BoxGeometry(0.5, 0.5, 0.5),
                new THREE.SphereGeometry(0.3, 8, 6),
                new THREE.OctahedronGeometry(0.4, 0)
            ];

            const material = new THREE.MeshBasicMaterial({ 
                color: 0x8a2be2, 
                wireframe: true,
                transparent: true,
                opacity: 0.6
            });

            for (let i = 0; i < 20; i++) {
                const geometry = geometries[Math.floor(Math.random() * geometries.length)];
                const mesh = new THREE.Mesh(geometry, material);
                
                mesh.position.x = (Math.random() - 0.5) * 20;
                mesh.position.y = (Math.random() - 0.5) * 20;
                mesh.position.z = (Math.random() - 0.5) * 20;
                
                mesh.rotation.x = Math.random() * 2 * Math.PI;
                mesh.rotation.y = Math.random() * 2 * Math.PI;
                
                scene.add(mesh);
            }

            camera.position.z = 5;
        }

        function animate() {
            requestAnimationFrame(animate);

            // Rotate particles
            if (particles) {
                particles.rotation.x += 0.001;
                particles.rotation.y += 0.002;
            }

            // Rotate geometric shapes
            scene.children.forEach(child => {
                if (child.type === 'Mesh') {
                    child.rotation.x += 0.01;
                    child.rotation.y += 0.01;
                }
            });

            renderer.render(scene, camera);
        }

        // Handle window resize
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            });
        });

        // Initialize 3D scene
        init3D();
        animate();

        // Event listeners
        window.addEventListener('resize', onWindowResize);

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe service cards for animation
        document.querySelectorAll('.service-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';
            card.style.transition = 'all 0.6s ease';
            observer.observe(card);
        });

        // Counter animation for stats
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            counters.forEach(counter => {
                const target = parseInt(counter.textContent);
                const increment = target / 50;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        counter.textContent = target + (counter.textContent.includes('%') ? '%' : '+');
                        clearInterval(timer);
                    } else {
                        counter.textContent = Math.floor(current) + (counter.textContent.includes('%') ? '%' : '+');
                    }
                }, 50);
            });
        }

        // Trigger counter animation when stats section is visible
        const statsSection = document.querySelector('.stats');
        const statsObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    statsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });

        statsObserver.observe(statsSection);
    </script>
</body>
</html>