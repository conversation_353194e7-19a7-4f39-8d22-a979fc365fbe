<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevCraft Studios - Railway-Inspired Development</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-violet: #8a2be2;
            --dark-violet: #6a1b9a;
            --light-violet: #9d4edd;
            --bg-dark: #0a0a0a;
            --bg-card: #1a1a1a;
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --border-color: rgba(138, 43, 226, 0.3);
            --gradient-primary: linear-gradient(135deg, #8a2be2, #6a1b9a);
            --train-track: #333;
            --success-green: #10b981;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-dark);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(10, 10, 10, 0.9);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            padding: 16px 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 32px;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--primary-violet);
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            background: radial-gradient(circle at 50% 50%, rgba(138, 43, 226, 0.1) 0%, transparent 70%);
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            border-radius: 24px;
            font-size: 14px;
            margin-bottom: 24px;
            backdrop-filter: blur(10px);
        }

        .hero-title {
            font-size: clamp(48px, 8vw, 80px);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 24px;
            letter-spacing: -0.02em;
        }

        .hero-highlight {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 20px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto 40px;
        }

        .hero-buttons {
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(138, 43, 226, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--primary-violet);
        }

        /* Main Value Proposition */
        .value-section {
            padding: 120px 0;
            background: linear-gradient(180deg, transparent 0%, rgba(138, 43, 226, 0.05) 50%, transparent 100%);
        }

        .value-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            text-align: center;
        }

        .value-title {
            font-size: clamp(36px, 6vw, 64px);
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 32px;
        }

        .value-subtitle {
            font-size: 24px;
            color: var(--text-secondary);
            margin-bottom: 60px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* SDLC Train Section */
        .sdlc-train-section {
            padding: 120px 0;
            background: var(--bg-card);
            position: relative;
            overflow: hidden;
        }

        .train-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .train-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .train-title {
            font-size: clamp(32px, 5vw, 48px);
            font-weight: 700;
            margin-bottom: 16px;
        }

        .train-subtitle {
            font-size: 20px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Train Track */
        .train-track {
            position: relative;
            height: 400px;
            background: linear-gradient(90deg, transparent 0%, var(--train-track) 2%, var(--train-track) 98%, transparent 100%);
            border-radius: 4px;
            margin: 60px 0;
            overflow: hidden;
        }

        .train-track::before,
        .train-track::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 100%;
            height: 2px;
            background: repeating-linear-gradient(90deg, var(--primary-violet) 0px, var(--primary-violet) 20px, transparent 20px, transparent 40px);
            transform: translateY(-50%);
        }

        .train-track::before {
            top: 40%;
        }

        .train-track::after {
            top: 60%;
        }

        /* Moving Train */
        .train {
            position: absolute;
            top: 50%;
            left: -200px;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            animation: trainMove 20s linear infinite;
        }

        .train-engine {
            width: 80px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 8px 0 0 8px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }

        .train-car {
            width: 120px;
            height: 50px;
            background: var(--bg-card);
            border: 2px solid var(--primary-violet);
            border-radius: 4px;
            margin-left: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px;
            position: relative;
        }

        .car-label {
            font-size: 10px;
            font-weight: 600;
            color: var(--primary-violet);
            margin-bottom: 4px;
        }

        .car-status {
            font-size: 8px;
            color: var(--text-secondary);
        }

        /* SDLC Stations */
        .sdlc-stations {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            position: relative;
        }

        .station {
            text-align: center;
            flex: 1;
            padding: 0 16px;
        }

        .station-icon {
            width: 60px;
            height: 60px;
            background: var(--bg-card);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 24px;
            transition: all 0.3s ease;
        }

        .station.active .station-icon {
            background: var(--gradient-primary);
            border-color: var(--primary-violet);
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(138, 43, 226, 0.5);
        }

        .station-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .station-desc {
            font-size: 14px;
            color: var(--text-secondary);
        }

        @keyframes trainMove {
            0% {
                left: -300px;
            }
            100% {
                left: calc(100% + 100px);
            }
        }

        /* Dashboard Section */
        .dashboard-section {
            padding: 120px 0;
            background: var(--bg-dark);
        }

        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title {
            font-size: clamp(32px, 5vw, 48px);
            font-weight: 700;
            margin-bottom: 16px;
        }

        .section-subtitle {
            font-size: 20px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .dashboard-mockup {
            background: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .dashboard-header {
            background: rgba(255, 255, 255, 0.05);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 16px 24px;
        }

        .dashboard-tabs {
            display: flex;
            gap: 8px;
        }

        .tab {
            padding: 8px 16px;
            background: transparent;
            color: var(--text-secondary);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .tab.active {
            background: var(--primary-violet);
            color: white;
        }

        .tab:hover:not(.active) {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
        }

        .dashboard-content {
            padding: 32px;
        }

        .tab-panel {
            display: none;
        }

        .tab-panel.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        /* Project Status Cards */
        .project-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .status-icon {
            font-size: 24px;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(138, 43, 226, 0.1);
            border-radius: 8px;
        }

        .status-title {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .status-value {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Build Log */
        .build-log {
            background: #0d1117;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-line {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 4px 0;
            font-size: 14px;
        }

        .log-line.active {
            background: rgba(138, 43, 226, 0.1);
            border-radius: 4px;
            padding: 4px 8px;
        }

        .log-time {
            color: #6b7280;
            font-size: 12px;
            min-width: 60px;
        }

        .log-status {
            min-width: 16px;
        }

        .log-status.success {
            color: var(--success-green);
        }

        .log-status.loading {
            color: var(--primary-violet);
            animation: spin 1s linear infinite;
        }

        .log-message {
            color: var(--text-primary);
        }

        /* Deployment Flow */
        .deployment-flow {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .deploy-step {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 4px solid transparent;
        }

        .deploy-step.completed {
            border-left-color: var(--success-green);
        }

        .deploy-step.active {
            border-left-color: var(--primary-violet);
            background: rgba(138, 43, 226, 0.1);
        }

        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.1);
        }

        .deploy-step.completed .step-icon {
            background: var(--success-green);
            color: white;
        }

        .deploy-step.active .step-icon {
            background: var(--primary-violet);
            color: white;
            animation: pulse 2s ease-in-out infinite;
        }

        .step-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .step-desc {
            font-size: 14px;
            color: var(--text-secondary);
        }

        /* Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
        }

        .metric-title {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 12px;
        }

        .metric-trend {
            font-size: 12px;
            font-weight: 500;
        }

        .metric-trend.up {
            color: var(--success-green);
        }

        .metric-chart {
            margin-top: 16px;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero {
                padding: 100px 0 60px;
            }
            
            .hero-buttons {
                flex-direction: column;
                align-items: stretch;
            }
            
            .train-track {
                height: 300px;
            }
            
            .sdlc-stations {
                flex-direction: column;
                gap: 32px;
            }
            
            .train-car {
                width: 80px;
                height: 40px;
                font-size: 8px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">DevCraft</div>
            <nav>
                <ul class="nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#process">Process</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <canvas id="three-canvas"></canvas>
        <div class="hero-container">
            <div class="hero-badge">
                <span>🚀</span>
                <span>Next-Generation Development</span>
            </div>
            <h1 class="hero-title">
                Building great products is hard.<br>
                <span class="hero-highlight">Scaling development is easy.</span>
            </h1>
            <p class="hero-subtitle">
                DevCraft Studios simplifies your development journey from concept to deployment with a comprehensive, scalable, easy-to-use development ecosystem.
            </p>
            <div class="hero-buttons">
                <a href="#process" class="btn btn-primary">
                    <span>See Our Process</span>
                    <span>→</span>
                </a>
                <a href="#contact" class="btn btn-secondary">Book a Demo</a>
            </div>
        </div>
    </section>

    <!-- Value Proposition Section -->
    <section class="value-section">
        <div class="value-container">
            <h2 class="value-title">
                Development shouldn't be complicated.<br>
                <span class="hero-highlight">Your journey should be seamless.</span>
            </h2>
            <p class="value-subtitle">
                We've reimagined the entire development lifecycle to be as smooth as a train on tracks - predictable, efficient, and always moving forward.
            </p>
        </div>
    </section>

    <!-- SDLC Train Section -->
    <section class="sdlc-train-section" id="process">
        <div class="train-container">
            <div class="train-header">
                <h2 class="train-title">Your Development Journey</h2>
                <p class="train-subtitle">
                    Watch your project move through our streamlined development process, from initial concept to final deployment.
                </p>
            </div>

            <!-- Train Track with Moving Train -->
            <div class="train-track">
                <div class="train" id="developmentTrain">
                    <div class="train-engine">DEV</div>
                    <div class="train-car">
                        <div class="car-label">DESIGN</div>
                        <div class="car-status">In Progress</div>
                    </div>
                    <div class="train-car">
                        <div class="car-label">BUILD</div>
                        <div class="car-status">Queued</div>
                    </div>
                    <div class="train-car">
                        <div class="car-label">TEST</div>
                        <div class="car-status">Pending</div>
                    </div>
                    <div class="train-car">
                        <div class="car-label">DEPLOY</div>
                        <div class="car-status">Ready</div>
                    </div>
                </div>
            </div>

            <!-- SDLC Stations -->
            <div class="sdlc-stations">
                <div class="station active" data-stage="planning">
                    <div class="station-icon">📋</div>
                    <div class="station-name">Planning</div>
                    <div class="station-desc">Requirements & Strategy</div>
                </div>
                <div class="station" data-stage="design">
                    <div class="station-icon">🎨</div>
                    <div class="station-name">Design</div>
                    <div class="station-desc">UI/UX & Prototyping</div>
                </div>
                <div class="station" data-stage="development">
                    <div class="station-icon">⚡</div>
                    <div class="station-name">Development</div>
                    <div class="station-desc">Coding & Integration</div>
                </div>
                <div class="station" data-stage="testing">
                    <div class="station-icon">🧪</div>
                    <div class="station-name">Testing</div>
                    <div class="station-desc">QA & Optimization</div>
                </div>
                <div class="station" data-stage="deployment">
                    <div class="station-icon">🚀</div>
                    <div class="station-name">Deployment</div>
                    <div class="station-desc">Launch & Monitoring</div>
                </div>
                <div class="station" data-stage="maintenance">
                    <div class="station-icon">🔧</div>
                    <div class="station-name">Maintenance</div>
                    <div class="station-desc">Support & Updates</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Development Dashboard -->
    <section class="dashboard-section">
        <div class="dashboard-container">
            <div class="section-header">
                <h2 class="section-title">Real-time Development Dashboard</h2>
                <p class="section-subtitle">Experience the power of our development environment</p>
            </div>

            <div class="dashboard-mockup">
                <div class="dashboard-header">
                    <div class="dashboard-tabs">
                        <div class="tab active" data-tab="overview">Overview</div>
                        <div class="tab" data-tab="build">Build</div>
                        <div class="tab" data-tab="deploy">Deploy</div>
                        <div class="tab" data-tab="monitor">Monitor</div>
                    </div>
                </div>

                <div class="dashboard-content">
                    <div class="tab-panel active" id="overview-panel">
                        <div class="project-status">
                            <div class="status-card">
                                <div class="status-icon">✅</div>
                                <div class="status-info">
                                    <div class="status-title">Build Status</div>
                                    <div class="status-value">Successful</div>
                                </div>
                            </div>
                            <div class="status-card">
                                <div class="status-icon">⚡</div>
                                <div class="status-info">
                                    <div class="status-title">Deploy Time</div>
                                    <div class="status-value">2m 34s</div>
                                </div>
                            </div>
                            <div class="status-card">
                                <div class="status-icon">📊</div>
                                <div class="status-info">
                                    <div class="status-title">Performance</div>
                                    <div class="status-value">98/100</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-panel" id="build-panel">
                        <div class="build-log">
                            <div class="log-line">
                                <span class="log-time">14:32:01</span>
                                <span class="log-status success">✓</span>
                                <span class="log-message">Installing dependencies...</span>
                            </div>
                            <div class="log-line">
                                <span class="log-time">14:32:15</span>
                                <span class="log-status success">✓</span>
                                <span class="log-message">Building application...</span>
                            </div>
                            <div class="log-line">
                                <span class="log-time">14:32:45</span>
                                <span class="log-status success">✓</span>
                                <span class="log-message">Build completed successfully</span>
                            </div>
                            <div class="log-line active">
                                <span class="log-time">14:32:46</span>
                                <span class="log-status loading">⟳</span>
                                <span class="log-message">Preparing deployment...</span>
                            </div>
                        </div>
                    </div>

                    <div class="tab-panel" id="deploy-panel">
                        <div class="deployment-flow">
                            <div class="deploy-step completed">
                                <div class="step-icon">✓</div>
                                <div class="step-info">
                                    <div class="step-title">Code Review</div>
                                    <div class="step-desc">All checks passed</div>
                                </div>
                            </div>
                            <div class="deploy-step completed">
                                <div class="step-icon">✓</div>
                                <div class="step-info">
                                    <div class="step-title">Build</div>
                                    <div class="step-desc">Compiled successfully</div>
                                </div>
                            </div>
                            <div class="deploy-step active">
                                <div class="step-icon">⟳</div>
                                <div class="step-info">
                                    <div class="step-title">Deploy</div>
                                    <div class="step-desc">Deploying to production</div>
                                </div>
                            </div>
                            <div class="deploy-step">
                                <div class="step-icon">○</div>
                                <div class="step-info">
                                    <div class="step-title">Verify</div>
                                    <div class="step-desc">Health checks pending</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-panel" id="monitor-panel">
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-title">Response Time</div>
                                <div class="metric-value">127ms</div>
                                <div class="metric-chart">
                                    <canvas id="responseChart" width="100" height="40"></canvas>
                                </div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-title">Uptime</div>
                                <div class="metric-value">99.9%</div>
                                <div class="metric-trend up">↗ +0.1%</div>
                            </div>
                            <div class="metric-card">
                                <div class="metric-title">Requests</div>
                                <div class="metric-value">1.2K/min</div>
                                <div class="metric-trend up">↗ +15%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Three.js Background
        let scene, camera, renderer, particles;

        function init3D() {
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            renderer = new THREE.WebGLRenderer({
                canvas: document.getElementById('three-canvas'),
                alpha: true
            });
            renderer.setSize(window.innerWidth, window.innerHeight);

            // Create particles
            const particlesGeometry = new THREE.BufferGeometry();
            const particlesCount = 800;
            const posArray = new Float32Array(particlesCount * 3);

            for(let i = 0; i < particlesCount * 3; i++) {
                posArray[i] = (Math.random() - 0.5) * 10;
            }

            particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));

            const particlesMaterial = new THREE.PointsMaterial({
                size: 0.005,
                color: '#8a2be2',
                transparent: true,
                opacity: 0.8
            });

            particles = new THREE.Points(particlesGeometry, particlesMaterial);
            scene.add(particles);

            camera.position.z = 3;
        }

        function animate() {
            requestAnimationFrame(animate);

            if (particles) {
                particles.rotation.x += 0.0005;
                particles.rotation.y += 0.0005;
            }

            renderer.render(scene, camera);
        }

        // SDLC Train Animation
        function initTrainAnimation() {
            const stations = document.querySelectorAll('.station');
            const trainCars = document.querySelectorAll('.train-car');
            let currentStation = 0;

            function updateStations() {
                stations.forEach((station, index) => {
                    station.classList.toggle('active', index === currentStation);
                });

                // Update train car statuses
                trainCars.forEach((car, index) => {
                    const statusEl = car.querySelector('.car-status');
                    if (index < currentStation) {
                        statusEl.textContent = 'Completed';
                        car.style.borderColor = '#10b981';
                    } else if (index === currentStation) {
                        statusEl.textContent = 'In Progress';
                        car.style.borderColor = '#8a2be2';
                    } else {
                        statusEl.textContent = 'Pending';
                        car.style.borderColor = '#333';
                    }
                });

                currentStation = (currentStation + 1) % stations.length;
            }

            // Update stations every 3 seconds
            setInterval(updateStations, 3000);
        }

        // Dashboard Tabs
        function initDashboard() {
            const tabs = document.querySelectorAll('.tab');
            const panels = document.querySelectorAll('.tab-panel');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const targetPanel = tab.getAttribute('data-tab');

                    tabs.forEach(t => t.classList.remove('active'));
                    panels.forEach(p => p.classList.remove('active'));

                    tab.classList.add('active');
                    document.getElementById(targetPanel + '-panel').classList.add('active');
                });
            });
        }

        // Initialize everything
        document.addEventListener('DOMContentLoaded', () => {
            init3D();
            animate();
            initTrainAnimation();
            initDashboard();
        });

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
    </script>
</body>
</html>
