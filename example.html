<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevCraft Studios - Web Development Excellence</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-violet: #8a2be2;
            --dark-violet: #6a1b9a;
            --light-violet: #9d4edd;
            --bg-dark: #0a0a0a;
            --bg-card: #111111;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --border-color: rgba(138, 43, 226, 0.2);
            --gradient-primary: linear-gradient(135deg, #8a2be2 0%, #6a1b9a 100%);
            --shadow-primary: 0 20px 40px rgba(138, 43, 226, 0.3);
            --train-track: #333;
            --success-green: #10b981;
            --warning-orange: #f59e0b;
            --error-red: #ef4444;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-dark);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            font-feature-settings: 'liga' 1, 'kern' 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Header - Railway Style */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 16px 0;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .header.scrolled {
            background: rgba(10, 10, 10, 0.98);
            border-bottom: 1px solid rgba(138, 43, 226, 0.3);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logo:hover {
            transform: scale(1.05);
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 32px;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s ease;
            letter-spacing: -0.01em;
            position: relative;
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: width 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--text-primary);
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        .cta-nav {
            background: var(--gradient-primary);
            color: white !important;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .cta-nav:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-primary);
        }

        .cta-nav::after {
            display: none;
        }

        /* Hero Section - Railway Inspired */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            padding: 120px 0 80px;
            overflow: hidden;
        }

        #three-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0.8;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            position: relative;
            z-index: 2;
        }

        .hero-content {
            max-width: 800px;
            animation: heroSlideIn 1s ease-out;
        }

        @keyframes heroSlideIn {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid var(--border-color);
            border-radius: 50px;
            padding: 6px 16px;
            font-size: 14px;
            font-weight: 500;
            color: var(--primary-violet);
            margin-bottom: 24px;
            backdrop-filter: blur(10px);
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(138, 43, 226, 0.4);
            }
            50% {
                box-shadow: 0 0 0 10px rgba(138, 43, 226, 0);
            }
        }

        .hero-title {
            font-size: clamp(48px, 8vw, 80px);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 24px;
            letter-spacing: -0.04em;
            background: linear-gradient(135deg, #ffffff 0%, var(--primary-violet) 50%, #ffffff 100%);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
        }

        .hero-subtitle {
            font-size: 20px;
            font-weight: 400;
            color: var(--text-secondary);
            margin-bottom: 40px;
            max-width: 600px;
            line-height: 1.5;
            animation: fadeInUp 0.8s ease-out 0.2s both;
        }

        .hero-buttons {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-family: inherit;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-primary);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Features Section - Railway Style */
        .features {
            padding: 120px 0;
            background: linear-gradient(180deg, transparent 0%, rgba(138, 43, 226, 0.02) 100%);
            position: relative;
        }

        .features::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, var(--primary-violet), transparent);
            opacity: 0.3;
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .section-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .section-badge {
            display: inline-block;
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid var(--border-color);
            border-radius: 50px;
            padding: 4px 12px;
            font-size: 12px;
            font-weight: 600;
            color: var(--primary-violet);
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .section-title {
            font-size: clamp(32px, 5vw, 48px);
            font-weight: 700;
            margin-bottom: 16px;
            letter-spacing: -0.02em;
        }

        .section-description {
            font-size: 18px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 32px;
            margin-top: 80px;
        }

        .feature-card {
            background: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 32px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at var(--x, 50%) var(--y, 50%), rgba(138, 43, 226, 0.1) 0%, transparent 50%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: var(--border-color);
            box-shadow: 0 25px 50px rgba(138, 43, 226, 0.2);
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-card:hover::after {
            opacity: 1;
        }

        .feature-icon {
            width: 56px;
            height: 56px;
            background: var(--gradient-primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 24px;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
            letter-spacing: -0.01em;
            transition: color 0.3s ease;
        }

        .feature-card:hover .feature-title {
            color: var(--primary-violet);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .feature-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 16px;
        }

        .feature-tag {
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 4px 12px;
            font-size: 12px;
            color: var(--primary-violet);
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-tag {
            background: rgba(138, 43, 226, 0.2);
            transform: translateY(-2px);
        }

        /* Social Proof Section */
        .social-proof {
            padding: 120px 0;
            background: var(--bg-card);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .social-proof::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(ellipse at center, rgba(138, 43, 226, 0.05) 0%, transparent 70%);
            pointer-events: none;
        }

        .social-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 48px;
            margin-top: 64px;
        }

        .stat-item {
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-8px);
        }

        .stat-number {
            font-size: 48px;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            letter-spacing: -0.02em;
            transition: all 0.3s ease;
        }

        .stat-item:hover .stat-number {
            transform: scale(1.1);
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 14px;
        }

        /* Testimonials */
        .testimonials {
            padding: 80px 0;
        }

        .testimonials-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 48px;
        }

        .testimonial-card {
            background: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .testimonial-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .testimonial-card:hover {
            border-color: var(--border-color);
            transform: translateY(-4px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }

        .testimonial-card:hover::before {
            transform: scaleX(1);
        }

        .testimonial-text {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .author-avatar {
            width: 32px;
            height: 32px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            transition: transform 0.3s ease;
        }

        .testimonial-card:hover .author-avatar {
            transform: scale(1.1);
        }

        .author-info h4 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .author-info p {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* CTA Section */
        .cta-section {
            padding: 120px 0;
            background: linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, transparent 100%);
            position: relative;
            overflow: hidden;
        }

        .cta-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(138, 43, 226, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 24px;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .cta-title {
            font-size: clamp(32px, 5vw, 48px);
            font-weight: 700;
            margin-bottom: 24px;
            letter-spacing: -0.02em;
        }

        .cta-description {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 40px;
        }

        /* Scroll animations */
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .animate-on-scroll.animated {
            opacity: 1;
            transform: translateY(0);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero {
                padding: 100px 0 60px;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: stretch;
            }

            .features {
                padding: 80px 0;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 24px;
                margin-top: 48px;
            }

            .feature-card {
                padding: 24px;
            }

            .social-proof {
                padding: 80px 0;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 32px;
            }

            .testimonials-grid {
                grid-template-columns: 1fr;
            }

            .cta-section {
                padding: 80px 0;
            }

            /* New sections responsive */
            .logos-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 16px;
            }

            .tech-tabs {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .tech-visual {
                padding: 16px;
            }

            .result-stats {
                flex-direction: column;
                gap: 12px;
            }

            .monitor-grid {
                grid-template-columns: 1fr;
            }

            .metric {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .metric-bar {
                width: 100%;
            }

            .security-item {
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }

            .editor-content {
                font-size: 12px;
            }

            .deployment-arrow {
                font-size: 20px;
                margin: 16px 0;
            }

            /* Value proposition responsive */
            .value-proposition {
                padding: 80px 0;
            }

            .value-title {
                font-size: clamp(28px, 6vw, 48px);
            }

            .value-description {
                font-size: 18px;
            }

            .value-actions {
                flex-direction: column;
                align-items: stretch;
            }

            .dev-flow {
                padding: 20px;
            }

            .flow-visual {
                grid-template-columns: 1fr;
                gap: 24px;
            }

            .flow-tabs {
                overflow-x: auto;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .flow-tabs::-webkit-scrollbar {
                display: none;
            }

            .timeline-steps {
                gap: 16px;
            }

            .panel-features {
                gap: 6px;
            }

            .feature-tag {
                font-size: 11px;
                padding: 3px 8px;
            }

            /* Railway sections responsive */
            .railway-value-section {
                padding: 80px 0;
            }

            .value-title {
                font-size: clamp(28px, 6vw, 48px);
            }

            .value-subtitle {
                font-size: 18px;
            }

            .sdlc-train-section {
                padding: 80px 0;
            }

            .train-track {
                height: 300px;
            }

            .train-car {
                width: 80px;
                height: 40px;
                font-size: 8px;
            }

            .car-label {
                font-size: 8px;
            }

            .car-status {
                font-size: 7px;
            }

            .train-engine {
                width: 60px;
                height: 45px;
                font-size: 10px;
            }

            .sdlc-stations {
                flex-direction: column;
                gap: 32px;
            }

            .station-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }

            /* Railway dashboard responsive */
            .railway-dashboard {
                grid-template-columns: 1fr;
                gap: 0;
            }

            .dashboard-sidebar {
                padding: 16px 0;
            }

            .dashboard-nav {
                flex-direction: row;
                overflow-x: auto;
                padding: 0 16px;
                gap: 4px;
            }

            .nav-item {
                flex-shrink: 0;
                padding: 8px 16px;
                border-right: none;
                border-bottom: 3px solid transparent;
                border-radius: 6px;
            }

            .nav-item.active {
                border-bottom-color: var(--primary-violet);
                border-right: none;
            }

            .dashboard-main {
                padding: 20px;
            }

            .metrics-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 16px;
            }

            .metric-card {
                padding: 16px;
            }

            .metric-value {
                font-size: 24px;
            }

            .monitoring-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .build-item,
            .deployment-item {
                padding: 16px;
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
            }

            .build-time,
            .deployment-time {
                text-align: left;
            }
        }

        /* Smooth scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Loading animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Mouse cursor effects */
        .cursor-glow {
            position: fixed;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, rgba(138, 43, 226, 0.8) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transform: translate(-50%, -50%);
            transition: transform 0.1s ease;
        }

        /* Dashboard Demo Section - Railway Style */
        .dashboard-demo {
            padding: 120px 0;
            background: linear-gradient(180deg, var(--bg-dark) 0%, rgba(138, 43, 226, 0.03) 50%, var(--bg-dark) 100%);
            position: relative;
            overflow: hidden;
        }

        .dashboard-demo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 30% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(138, 43, 226, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .dashboard-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            position: relative;
            z-index: 2;
        }

        .dashboard-mockup {
            background: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            overflow: hidden;
            margin-top: 60px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
            transition: all 0.4s ease;
        }

        .dashboard-mockup:hover {
            transform: translateY(-8px);
            box-shadow: 0 35px 70px rgba(138, 43, 226, 0.2);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            background: rgba(255, 255, 255, 0.02);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dashboard-tabs {
            display: flex;
            gap: 24px;
        }

        .tab {
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            border-radius: 6px;
            transition: all 0.2s ease;
            position: relative;
        }

        .tab.active {
            color: var(--primary-violet);
            background: rgba(138, 43, 226, 0.1);
        }

        .tab:hover:not(.active) {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.05);
        }

        .dashboard-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            animation: pulse-green 2s ease-in-out infinite;
        }

        @keyframes pulse-green {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
            }
            50% {
                box-shadow: 0 0 0 6px rgba(16, 185, 129, 0);
            }
        }

        .dashboard-content {
            padding: 32px 24px;
            min-height: 300px;
        }

        .tab-content {
            display: none;
            animation: fadeInUp 0.4s ease;
        }

        .tab-content.active {
            display: block;
        }

        /* Deployment Flow */
        .deployment-flow {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .deploy-step {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
        }

        .deploy-step.completed {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .deploy-step.active {
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid var(--border-color);
        }

        .deploy-step:not(.completed):not(.active) {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .step-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .deploy-step.completed .step-icon {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
        }

        .deploy-step.active .step-icon {
            background: rgba(138, 43, 226, 0.2);
            color: var(--primary-violet);
            animation: spin 2s linear infinite;
        }

        .deploy-step:not(.completed):not(.active) .step-icon {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .step-info {
            flex: 1;
        }

        .step-info h4 {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .step-info p {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .step-time {
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--border-color);
            transform: translateY(-2px);
        }

        .metric-card h4 {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
            font-weight: 500;
        }

        .metric-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--primary-violet);
            margin-bottom: 12px;
        }

        .metric-chart {
            height: 40px;
            opacity: 0.7;
        }

        .metric-chart canvas {
            width: 100%;
            height: 100%;
        }

        /* Scaling Info */
        .scaling-info {
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }

        .scale-metric {
            margin-bottom: 32px;
        }

        .scale-metric h4 {
            font-size: 16px;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .load-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .load-fill {
            height: 100%;
            background: var(--gradient-primary);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .scale-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        .scale-btn {
            padding: 8px 16px;
            border: 1px solid var(--border-color);
            background: rgba(138, 43, 226, 0.1);
            color: var(--primary-violet);
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .scale-btn:hover {
            background: rgba(138, 43, 226, 0.2);
            transform: translateY(-1px);
        }

        .scale-btn.secondary {
            background: transparent;
            color: var(--text-secondary);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .scale-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
        }

        /* Railway-Style Value Section */
        .railway-value-section {
            padding: 120px 0;
            background: linear-gradient(180deg, transparent 0%, rgba(138, 43, 226, 0.05) 50%, transparent 100%);
            position: relative;
        }

        .railway-value-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(138,43,226,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
            pointer-events: none;
        }

        .value-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            text-align: center;
            position: relative;
            z-index: 2;
        }

        .value-title {
            font-size: clamp(36px, 6vw, 64px);
            font-weight: 700;
            line-height: 1.2;
            margin-bottom: 32px;
            letter-spacing: -0.02em;
        }

        .value-subtitle {
            font-size: 24px;
            color: var(--text-secondary);
            margin-bottom: 60px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            line-height: 1.6;
        }

        /* SDLC Train Section */
        .sdlc-train-section {
            padding: 120px 0;
            background: var(--bg-card);
            position: relative;
            overflow: hidden;
        }

        .train-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .train-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .train-title {
            font-size: clamp(32px, 5vw, 48px);
            font-weight: 700;
            margin-bottom: 16px;
        }

        .train-subtitle {
            font-size: 20px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        /* Train Track */
        .train-track {
            position: relative;
            height: 400px;
            background: linear-gradient(90deg, transparent 0%, var(--train-track) 2%, var(--train-track) 98%, transparent 100%);
            border-radius: 4px;
            margin: 60px 0;
            overflow: hidden;
        }

        .train-track::before,
        .train-track::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 100%;
            height: 2px;
            background: repeating-linear-gradient(90deg, var(--primary-violet) 0px, var(--primary-violet) 20px, transparent 20px, transparent 40px);
            transform: translateY(-50%);
        }

        .train-track::before {
            top: 40%;
        }

        .train-track::after {
            top: 60%;
        }

        /* Moving Train */
        .train {
            position: absolute;
            top: 50%;
            left: -200px;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            animation: trainMove 20s linear infinite;
            z-index: 10;
        }

        .train-engine {
            width: 80px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 8px 0 0 8px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            box-shadow: 0 4px 15px rgba(138, 43, 226, 0.4);
        }

        .train-car {
            width: 120px;
            height: 50px;
            background: var(--bg-card);
            border: 2px solid var(--primary-violet);
            border-radius: 4px;
            margin-left: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 8px;
            position: relative;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .car-label {
            font-size: 10px;
            font-weight: 600;
            color: var(--primary-violet);
            margin-bottom: 4px;
        }

        .car-status {
            font-size: 8px;
            color: var(--text-secondary);
        }

        /* SDLC Stations */
        .sdlc-stations {
            display: flex;
            justify-content: space-between;
            margin-top: 40px;
            position: relative;
        }

        .station {
            text-align: center;
            flex: 1;
            padding: 0 16px;
        }

        .station-icon {
            width: 60px;
            height: 60px;
            background: var(--bg-card);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            font-size: 24px;
            transition: all 0.3s ease;
        }

        .station.active .station-icon {
            background: var(--gradient-primary);
            border-color: var(--primary-violet);
            transform: scale(1.1);
            box-shadow: 0 0 20px rgba(138, 43, 226, 0.5);
        }

        .station-name {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .station-desc {
            font-size: 14px;
            color: var(--text-secondary);
        }

        @keyframes trainMove {
            0% {
                left: -300px;
            }
            100% {
                left: calc(100% + 100px);
            }
        }

        /* Railway Dashboard Section */
        .railway-dashboard-section {
            padding: 120px 0;
            background: var(--bg-dark);
            position: relative;
        }

        .railway-dashboard-section .section-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .railway-dashboard-section .section-badge {
            display: inline-block;
            background: rgba(138, 43, 226, 0.1);
            color: var(--primary-violet);
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 16px;
            border: 1px solid var(--border-color);
        }

        .railway-dashboard-section .section-title {
            font-size: clamp(32px, 5vw, 48px);
            font-weight: 700;
            margin-bottom: 16px;
        }

        .railway-dashboard-section .section-subtitle {
            font-size: 20px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .railway-dashboard {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 32px;
            background: var(--bg-card);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dashboard-sidebar {
            background: rgba(255, 255, 255, 0.05);
            padding: 24px 0;
        }

        .dashboard-nav {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 24px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
        }

        .nav-item.active {
            background: rgba(138, 43, 226, 0.1);
            color: var(--primary-violet);
            border-right-color: var(--primary-violet);
        }

        .nav-icon {
            font-size: 18px;
            width: 24px;
            text-align: center;
        }

        .dashboard-main {
            padding: 32px;
        }

        .dashboard-panel {
            display: none;
        }

        .dashboard-panel.active {
            display: block;
            animation: fadeInUp 0.5s ease;
        }

        .panel-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .panel-header h3 {
            font-size: 24px;
            font-weight: 600;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-dot.success {
            background: var(--success-green);
            animation: pulse-green 2s ease-in-out infinite;
        }

        .status-dot.building {
            background: var(--warning-orange);
            animation: pulse-orange 2s ease-in-out infinite;
        }

        .status-dot.queued {
            background: var(--text-secondary);
        }

        .action-btn {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(138, 43, 226, 0.3);
        }

        /* Metrics Grid */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .metric-title {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .metric-icon {
            font-size: 20px;
        }

        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .metric-change {
            font-size: 12px;
            font-weight: 500;
        }

        .metric-change.positive {
            color: var(--success-green);
        }

        .metric-change.negative {
            color: var(--error-red);
        }

        /* Activity Feed */
        .activity-feed h4 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .activity-item {
            display: flex;
            gap: 16px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid transparent;
        }

        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            flex-shrink: 0;
        }

        .activity-icon.success {
            background: var(--success-green);
            color: white;
        }

        .activity-icon.building {
            background: var(--warning-orange);
            color: white;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .activity-desc {
            font-size: 13px;
            color: var(--text-secondary);
            margin-bottom: 4px;
        }

        .activity-time {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* Build Queue */
        .build-queue {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .build-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid transparent;
        }

        .build-item.active {
            border-left-color: var(--warning-orange);
            background: rgba(245, 158, 11, 0.1);
        }

        .build-status {
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 100px;
        }

        .build-info {
            flex: 1;
        }

        .build-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .build-branch {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .build-time {
            font-size: 14px;
            color: var(--text-secondary);
            min-width: 60px;
            text-align: right;
        }

        /* Deployment Timeline */
        .deployment-timeline {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .deployment-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            border-left: 3px solid transparent;
        }

        .deployment-item.success {
            border-left-color: var(--success-green);
        }

        .deployment-status {
            display: flex;
            align-items: center;
            gap: 8px;
            min-width: 100px;
        }

        .deployment-info {
            flex: 1;
        }

        .deployment-name {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .deployment-env {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .deployment-time {
            font-size: 14px;
            color: var(--text-secondary);
            min-width: 80px;
            text-align: right;
        }

        /* Monitoring Grid */
        .monitoring-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
        }

        .monitor-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
        }

        .monitor-title {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .monitor-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 12px;
        }

        .monitor-trend {
            font-size: 12px;
            font-weight: 500;
        }

        .monitor-trend.good {
            color: var(--success-green);
        }

        .monitor-chart {
            margin-top: 16px;
        }

        .time-range select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
        }

        /* Animations */
        @keyframes pulse-green {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @keyframes pulse-orange {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Value Proposition Section */
        .value-proposition {
            padding: 120px 0;
            background:
                linear-gradient(180deg, rgba(138, 43, 226, 0.05) 0%, transparent 30%, transparent 70%, rgba(138, 43, 226, 0.05) 100%),
                radial-gradient(circle at 30% 20%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(138, 43, 226, 0.08) 0%, transparent 50%);
            position: relative;
            overflow: hidden;
        }

        .value-proposition::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(138,43,226,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
            pointer-events: none;
        }

        .value-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            position: relative;
            z-index: 2;
        }

        .value-content {
            text-align: center;
            margin-bottom: 80px;
        }

        .value-title {
            font-size: clamp(36px, 5vw, 64px);
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 24px;
            color: var(--text-primary);
            letter-spacing: -0.02em;
        }

        .value-highlight {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .value-description {
            font-size: 20px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto 40px;
            line-height: 1.6;
        }

        .value-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }

        .btn-outline {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 12px 24px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-outline:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: var(--primary-violet);
            color: var(--primary-violet);
            transform: translateY(-1px);
        }

        /* Development Flow */
        .dev-flow {
            background: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        .flow-tabs {
            display: flex;
            gap: 8px;
            margin-bottom: 32px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 4px;
            overflow-x: auto;
        }

        .flow-tab {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .flow-tab.active {
            background: var(--primary-violet);
            color: white;
        }

        .flow-tab:hover:not(.active) {
            color: var(--text-primary);
            background: rgba(255, 255, 255, 0.1);
        }

        .flow-visual {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 32px;
            align-items: start;
        }

        .flow-timeline {
            position: relative;
        }

        .timeline-progress {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 2px;
            height: calc(100% - 40px);
            background: linear-gradient(180deg, var(--primary-violet) 0%, var(--primary-violet) 30%, rgba(255, 255, 255, 0.2) 100%);
            border-radius: 1px;
        }

        .timeline-steps {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .timeline-step {
            display: flex;
            align-items: center;
            gap: 16px;
            position: relative;
        }

        .step-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .timeline-step.active .step-dot {
            background: var(--primary-violet);
            border-color: var(--primary-violet);
            box-shadow: 0 0 0 4px rgba(138, 43, 226, 0.2);
        }

        .step-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .timeline-step.active .step-label {
            color: var(--text-primary);
        }

        .flow-details {
            position: relative;
        }

        .flow-panel {
            display: none;
            animation: fadeInUp 0.5s ease;
        }

        .flow-panel.active {
            display: block;
        }

        .panel-content h4 {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 12px;
        }

        .panel-content p {
            font-size: 16px;
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .panel-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .feature-tag {
            background: rgba(138, 43, 226, 0.1);
            color: var(--primary-violet);
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid rgba(138, 43, 226, 0.2);
        }

        /* Company Logos Section */
        .company-logos {
            padding: 80px 0;
            background: linear-gradient(180deg, rgba(138, 43, 226, 0.02) 0%, transparent 100%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .logos-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            text-align: center;
        }

        .logos-subtitle {
            font-size: 16px;
            color: var(--text-secondary);
            margin-bottom: 40px;
            font-weight: 500;
        }

        .logos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 32px;
            align-items: center;
        }

        .logo-item {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 60px;
            transition: all 0.3s ease;
        }

        .company-logo {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-secondary);
            opacity: 0.6;
            transition: all 0.3s ease;
            letter-spacing: -0.02em;
        }

        .logo-item:hover .company-logo {
            color: var(--primary-violet);
            opacity: 1;
            transform: scale(1.05);
        }

        /* Technology Showcase Section */
        .tech-showcase {
            padding: 120px 0;
            background: linear-gradient(180deg, transparent 0%, rgba(138, 43, 226, 0.03) 50%, transparent 100%);
            position: relative;
        }

        .tech-showcase::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 30%, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(138, 43, 226, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .showcase-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            position: relative;
            z-index: 2;
        }

        .tech-tabs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin: 60px 0;
        }

        .tech-tab {
            background: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .tech-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--gradient-primary);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .tech-tab.active::before,
        .tech-tab:hover::before {
            transform: scaleX(1);
        }

        .tech-tab.active {
            background: rgba(138, 43, 226, 0.1);
            border-color: var(--border-color);
            transform: translateY(-4px);
        }

        .tech-tab:hover:not(.active) {
            background: rgba(255, 255, 255, 0.05);
            transform: translateY(-2px);
        }

        .tech-tab-icon {
            font-size: 32px;
            margin-bottom: 16px;
        }

        .tech-tab h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--text-primary);
        }

        .tech-tab p {
            font-size: 14px;
            color: var(--text-secondary);
            margin: 0;
        }

        .tech-content {
            margin-top: 60px;
        }

        .tech-panel {
            display: none;
            animation: fadeInUp 0.5s ease;
        }

        .tech-panel.active {
            display: block;
        }

        .tech-visual {
            background: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        /* Code Editor Styles */
        .code-editor {
            background: #1a1a1a;
            border-radius: 8px;
            overflow: hidden;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            margin-bottom: 24px;
        }

        .editor-header {
            background: #2d2d2d;
            padding: 12px 16px;
            border-bottom: 1px solid #404040;
        }

        .editor-tabs {
            display: flex;
            gap: 8px;
        }

        .editor-tab {
            padding: 6px 12px;
            background: #404040;
            border-radius: 4px;
            font-size: 12px;
            color: #ccc;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .editor-tab.active {
            background: var(--primary-violet);
            color: white;
        }

        .editor-content {
            padding: 16px;
            background: #1a1a1a;
            font-size: 14px;
            line-height: 1.5;
        }

        .code-line {
            margin-bottom: 4px;
        }

        .code-keyword { color: #ff79c6; }
        .code-variable { color: #8be9fd; }
        .code-function { color: #50fa7b; }
        .code-string { color: #f1fa8c; }
        .code-property { color: #bd93f9; }
        .code-number { color: #ffb86c; }

        .deployment-arrow {
            text-align: center;
            font-size: 24px;
            color: var(--primary-violet);
            margin: 24px 0;
            animation: pulse 2s ease-in-out infinite;
        }

        .deployment-result {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.2);
            border-radius: 8px;
            padding: 20px;
        }

        .result-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-dot.success {
            background: #10b981;
            animation: pulse-green 2s ease-in-out infinite;
        }

        .result-url {
            font-family: monospace;
            color: var(--primary-violet);
            margin-bottom: 16px;
            font-size: 14px;
        }

        .result-stats {
            display: flex;
            gap: 24px;
        }

        .stat {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* Scaling Dashboard */
        .scaling-dashboard h4,
        .monitoring-dashboard h4,
        .security-dashboard h4 {
            margin-bottom: 24px;
            color: var(--text-primary);
            font-size: 18px;
        }

        .scaling-metrics {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 24px;
        }

        .metric {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .metric-label {
            min-width: 100px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .metric-bar {
            flex: 1;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .metric-fill {
            height: 100%;
            background: var(--gradient-primary);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .metric-value {
            min-width: 50px;
            text-align: right;
            font-weight: 600;
            color: var(--primary-violet);
        }

        .scaling-action {
            padding: 16px;
            background: rgba(138, 43, 226, 0.1);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .action-indicator {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .pulse-dot {
            width: 8px;
            height: 8px;
            background: var(--primary-violet);
            border-radius: 50%;
            animation: pulse 1.5s ease-in-out infinite;
        }

        /* Monitoring Dashboard */
        .monitor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
        }

        .monitor-card {
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }

        .monitor-title {
            font-size: 12px;
            color: var(--text-secondary);
            margin-bottom: 8px;
        }

        .monitor-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .monitor-trend {
            font-size: 12px;
            font-weight: 500;
        }

        .monitor-trend.up { color: #10b981; }
        .monitor-trend.down { color: #ef4444; }
        .monitor-trend.stable { color: var(--text-secondary); }

        /* Security Dashboard */
        .security-features {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .security-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            background: rgba(255, 255, 255, 0.02);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .security-icon {
            font-size: 24px;
        }

        .security-info {
            flex: 1;
        }

        .security-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .security-status {
            font-size: 12px;
            color: #10b981;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <div class="cursor-glow" id="cursor-glow"></div>

    <!-- Header -->
    <header class="header" id="header">
        <div class="nav-container">
            <div class="logo">DevCraft</div>
            <nav>
                <ul class="nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#work">Work</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#contact" class="cta-nav">Get Started</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <canvas id="three-canvas"></canvas>
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <span>✨</span>
                    <span>Crafting Digital Excellence</span>
                </div>
                <h1 class="hero-title">
                    Building great products is hard.<br>
                    <span class="hero-highlight">Scaling development is easy.</span>
                </h1>
                <p class="hero-subtitle">
                    DevCraft Studios simplifies your development journey from concept to deployment with a comprehensive, scalable, easy-to-use development ecosystem.
                </p>
                <div class="hero-buttons">
                    <a href="#services" class="btn btn-primary">
                        <span>Our Services</span>
                        <span>→</span>
                    </a>
                    <a href="#work" class="btn btn-secondary">
                        <span>View Our Work</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Railway-Style Value Proposition -->
    <section class="railway-value-section">
        <div class="value-container">
            <h2 class="value-title">
                Development shouldn't be complicated.<br>
                <span class="hero-highlight">Your journey should be seamless.</span>
            </h2>
            <p class="value-subtitle">
                We've reimagined the entire development lifecycle to be as smooth as a train on tracks - predictable, efficient, and always moving forward.
            </p>
        </div>
    </section>

    <!-- SDLC Train Section -->
    <section class="sdlc-train-section" id="process">
        <div class="train-container">
            <div class="train-header">
                <h2 class="train-title">Your Development Journey</h2>
                <p class="train-subtitle">
                    Watch your project move through our streamlined development process, from initial concept to final deployment.
                </p>
            </div>

            <!-- Train Track with Moving Train -->
            <div class="train-track">
                <div class="train" id="developmentTrain">
                    <div class="train-engine">DEV</div>
                    <div class="train-car">
                        <div class="car-label">DESIGN</div>
                        <div class="car-status">In Progress</div>
                    </div>
                    <div class="train-car">
                        <div class="car-label">BUILD</div>
                        <div class="car-status">Queued</div>
                    </div>
                    <div class="train-car">
                        <div class="car-label">TEST</div>
                        <div class="car-status">Pending</div>
                    </div>
                    <div class="train-car">
                        <div class="car-label">DEPLOY</div>
                        <div class="car-status">Ready</div>
                    </div>
                </div>
            </div>

            <!-- SDLC Stations -->
            <div class="sdlc-stations">
                <div class="station active" data-stage="planning">
                    <div class="station-icon">📋</div>
                    <div class="station-name">Planning</div>
                    <div class="station-desc">Requirements & Strategy</div>
                </div>
                <div class="station" data-stage="design">
                    <div class="station-icon">🎨</div>
                    <div class="station-name">Design</div>
                    <div class="station-desc">UI/UX & Prototyping</div>
                </div>
                <div class="station" data-stage="development">
                    <div class="station-icon">⚡</div>
                    <div class="station-name">Development</div>
                    <div class="station-desc">Coding & Integration</div>
                </div>
                <div class="station" data-stage="testing">
                    <div class="station-icon">🧪</div>
                    <div class="station-name">Testing</div>
                    <div class="station-desc">QA & Optimization</div>
                </div>
                <div class="station" data-stage="deployment">
                    <div class="station-icon">🚀</div>
                    <div class="station-name">Deployment</div>
                    <div class="station-desc">Launch & Monitoring</div>
                </div>
                <div class="station" data-stage="maintenance">
                    <div class="station-icon">🔧</div>
                    <div class="station-name">Maintenance</div>
                    <div class="station-desc">Support & Updates</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Value Proposition Section -->
    <section class="value-proposition">
        <div class="value-container">
            <div class="value-content animate-on-scroll">
                <h2 class="value-title">
                    Building great products is hard.<br>
                    <span class="value-highlight">Scaling development is easy.</span>
                </h2>
                <p class="value-description">
                    DevCraft Studios simplifies your development journey from concept to deployment with a comprehensive, scalable, easy-to-use development ecosystem.
                </p>
                <div class="value-actions">
                    <a href="#services" class="btn btn-primary">
                        <span>Start Your Project</span>
                        <span>→</span>
                    </a>
                    <a href="#contact" class="btn btn-outline">Book a Consultation</a>
                </div>
            </div>

            <!-- Interactive Development Flow -->
            <div class="dev-flow animate-on-scroll">
                <div class="flow-tabs">
                    <div class="flow-tab active" data-flow="design">Design</div>
                    <div class="flow-tab" data-flow="develop">Develop</div>
                    <div class="flow-tab" data-flow="deploy">Deploy</div>
                    <div class="flow-tab" data-flow="scale">Scale</div>
                    <div class="flow-tab" data-flow="maintain">Maintain</div>
                </div>

                <div class="flow-visual">
                    <div class="flow-timeline">
                        <div class="timeline-progress"></div>
                        <div class="timeline-steps">
                            <div class="timeline-step active">
                                <div class="step-dot"></div>
                                <div class="step-label">Concept</div>
                            </div>
                            <div class="timeline-step">
                                <div class="step-dot"></div>
                                <div class="step-label">Design</div>
                            </div>
                            <div class="timeline-step">
                                <div class="step-dot"></div>
                                <div class="step-label">Build</div>
                            </div>
                            <div class="timeline-step">
                                <div class="step-dot"></div>
                                <div class="step-label">Test</div>
                            </div>
                            <div class="timeline-step">
                                <div class="step-dot"></div>
                                <div class="step-label">Deploy</div>
                            </div>
                            <div class="timeline-step">
                                <div class="step-dot"></div>
                                <div class="step-label">Scale</div>
                            </div>
                        </div>
                    </div>

                    <div class="flow-details">
                        <div class="flow-panel active" id="design-flow">
                            <div class="panel-content">
                                <h4>User-Centered Design</h4>
                                <p>We start with understanding your users and business goals to create intuitive, beautiful interfaces.</p>
                                <div class="panel-features">
                                    <span class="feature-tag">UI/UX Design</span>
                                    <span class="feature-tag">Prototyping</span>
                                    <span class="feature-tag">User Research</span>
                                </div>
                            </div>
                        </div>
                        <div class="flow-panel" id="develop-flow">
                            <div class="panel-content">
                                <h4>Modern Development</h4>
                                <p>Using cutting-edge technologies and best practices to build scalable, maintainable applications.</p>
                                <div class="panel-features">
                                    <span class="feature-tag">React/Next.js</span>
                                    <span class="feature-tag">Node.js</span>
                                    <span class="feature-tag">TypeScript</span>
                                </div>
                            </div>
                        </div>
                        <div class="flow-panel" id="deploy-flow">
                            <div class="panel-content">
                                <h4>Seamless Deployment</h4>
                                <p>Automated CI/CD pipelines ensure your application is deployed safely and efficiently.</p>
                                <div class="panel-features">
                                    <span class="feature-tag">CI/CD</span>
                                    <span class="feature-tag">Cloud Hosting</span>
                                    <span class="feature-tag">SSL/Security</span>
                                </div>
                            </div>
                        </div>
                        <div class="flow-panel" id="scale-flow">
                            <div class="panel-content">
                                <h4>Auto-Scaling Infrastructure</h4>
                                <p>Your application automatically scales to handle increased traffic and user demand.</p>
                                <div class="panel-features">
                                    <span class="feature-tag">Load Balancing</span>
                                    <span class="feature-tag">CDN</span>
                                    <span class="feature-tag">Performance</span>
                                </div>
                            </div>
                        </div>
                        <div class="flow-panel" id="maintain-flow">
                            <div class="panel-content">
                                <h4>Ongoing Support</h4>
                                <p>Continuous monitoring, updates, and support to keep your application running smoothly.</p>
                                <div class="panel-features">
                                    <span class="feature-tag">Monitoring</span>
                                    <span class="feature-tag">Updates</span>
                                    <span class="feature-tag">Support</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Railway-Style Interactive Dashboard -->
    <section class="railway-dashboard-section">
        <div class="dashboard-container">
            <div class="section-header">
                <div class="section-badge">Live Dashboard</div>
                <h2 class="section-title">Real-time Development Environment</h2>
                <p class="section-subtitle">Experience the power of our development ecosystem with live metrics and interactive controls</p>
            </div>

            <div class="railway-dashboard">
                <div class="dashboard-sidebar">
                    <div class="dashboard-nav">
                        <div class="nav-item active" data-panel="overview">
                            <div class="nav-icon">📊</div>
                            <span>Overview</span>
                        </div>
                        <div class="nav-item" data-panel="builds">
                            <div class="nav-icon">🔨</div>
                            <span>Builds</span>
                        </div>
                        <div class="nav-item" data-panel="deployments">
                            <div class="nav-icon">🚀</div>
                            <span>Deployments</span>
                        </div>
                        <div class="nav-item" data-panel="monitoring">
                            <div class="nav-icon">📈</div>
                            <span>Monitoring</span>
                        </div>
                    </div>
                </div>

                <div class="dashboard-main">
                    <!-- Overview Panel -->
                    <div class="dashboard-panel active" id="overview-panel">
                        <div class="panel-header">
                            <h3>Project Overview</h3>
                            <div class="status-indicator">
                                <div class="status-dot success"></div>
                                <span>All Systems Operational</span>
                            </div>
                        </div>

                        <div class="metrics-grid">
                            <div class="metric-card">
                                <div class="metric-header">
                                    <span class="metric-title">Active Projects</span>
                                    <span class="metric-icon">📁</span>
                                </div>
                                <div class="metric-value">24</div>
                                <div class="metric-change positive">+3 this week</div>
                            </div>

                            <div class="metric-card">
                                <div class="metric-header">
                                    <span class="metric-title">Deployments</span>
                                    <span class="metric-icon">🚀</span>
                                </div>
                                <div class="metric-value">156</div>
                                <div class="metric-change positive">+12 today</div>
                            </div>

                            <div class="metric-card">
                                <div class="metric-header">
                                    <span class="metric-title">Success Rate</span>
                                    <span class="metric-icon">✅</span>
                                </div>
                                <div class="metric-value">99.2%</div>
                                <div class="metric-change positive">+0.3%</div>
                            </div>

                            <div class="metric-card">
                                <div class="metric-header">
                                    <span class="metric-title">Avg Deploy Time</span>
                                    <span class="metric-icon">⏱️</span>
                                </div>
                                <div class="metric-value">2m 34s</div>
                                <div class="metric-change negative">-15s</div>
                            </div>
                        </div>

                        <div class="activity-feed">
                            <h4>Recent Activity</h4>
                            <div class="activity-list">
                                <div class="activity-item">
                                    <div class="activity-icon success">✓</div>
                                    <div class="activity-content">
                                        <div class="activity-title">Deployment successful</div>
                                        <div class="activity-desc">portfolio-website deployed to production</div>
                                        <div class="activity-time">2 minutes ago</div>
                                    </div>
                                </div>

                                <div class="activity-item">
                                    <div class="activity-icon building">⚡</div>
                                    <div class="activity-content">
                                        <div class="activity-title">Build started</div>
                                        <div class="activity-desc">e-commerce-app building from main branch</div>
                                        <div class="activity-time">5 minutes ago</div>
                                    </div>
                                </div>

                                <div class="activity-item">
                                    <div class="activity-icon success">✓</div>
                                    <div class="activity-content">
                                        <div class="activity-title">Tests passed</div>
                                        <div class="activity-desc">All 47 tests passed for mobile-app</div>
                                        <div class="activity-time">8 minutes ago</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Builds Panel -->
                    <div class="dashboard-panel" id="builds-panel">
                        <div class="panel-header">
                            <h3>Build Pipeline</h3>
                            <button class="action-btn">New Build</button>
                        </div>

                        <div class="build-queue">
                            <div class="build-item active">
                                <div class="build-status">
                                    <div class="status-dot building"></div>
                                    <span>Building</span>
                                </div>
                                <div class="build-info">
                                    <div class="build-name">portfolio-website</div>
                                    <div class="build-branch">main • #142</div>
                                </div>
                                <div class="build-time">2m 15s</div>
                            </div>

                            <div class="build-item">
                                <div class="build-status">
                                    <div class="status-dot queued"></div>
                                    <span>Queued</span>
                                </div>
                                <div class="build-info">
                                    <div class="build-name">e-commerce-app</div>
                                    <div class="build-branch">feature/checkout • #89</div>
                                </div>
                                <div class="build-time">-</div>
                            </div>
                        </div>
                    </div>

                    <!-- Deployments Panel -->
                    <div class="dashboard-panel" id="deployments-panel">
                        <div class="panel-header">
                            <h3>Deployment History</h3>
                            <button class="action-btn">Deploy Now</button>
                        </div>

                        <div class="deployment-timeline">
                            <div class="deployment-item success">
                                <div class="deployment-status">
                                    <div class="status-dot success"></div>
                                    <span>Deployed</span>
                                </div>
                                <div class="deployment-info">
                                    <div class="deployment-name">portfolio-website v2.1.0</div>
                                    <div class="deployment-env">Production</div>
                                </div>
                                <div class="deployment-time">2 min ago</div>
                            </div>

                            <div class="deployment-item success">
                                <div class="deployment-status">
                                    <div class="status-dot success"></div>
                                    <span>Deployed</span>
                                </div>
                                <div class="deployment-info">
                                    <div class="deployment-name">mobile-app v1.3.2</div>
                                    <div class="deployment-env">Staging</div>
                                </div>
                                <div class="deployment-time">1 hour ago</div>
                            </div>
                        </div>
                    </div>

                    <!-- Monitoring Panel -->
                    <div class="dashboard-panel" id="monitoring-panel">
                        <div class="panel-header">
                            <h3>System Monitoring</h3>
                            <div class="time-range">
                                <select>
                                    <option>Last 24 hours</option>
                                    <option>Last 7 days</option>
                                    <option>Last 30 days</option>
                                </select>
                            </div>
                        </div>

                        <div class="monitoring-grid">
                            <div class="monitor-card">
                                <div class="monitor-title">Response Time</div>
                                <div class="monitor-value">127ms</div>
                                <div class="monitor-chart">
                                    <canvas id="responseTimeChart" width="200" height="60"></canvas>
                                </div>
                            </div>

                            <div class="monitor-card">
                                <div class="monitor-title">Error Rate</div>
                                <div class="monitor-value">0.1%</div>
                                <div class="monitor-trend good">↓ -0.05%</div>
                            </div>

                            <div class="monitor-card">
                                <div class="monitor-title">Throughput</div>
                                <div class="monitor-value">1.2K req/min</div>
                                <div class="monitor-trend good">↑ +15%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Company Logos Section -->
    <section class="company-logos">
        <div class="logos-container">
            <div class="section-header animate-on-scroll">
                <p class="logos-subtitle">Trusted by innovative companies worldwide</p>
            </div>
            <div class="logos-grid animate-on-scroll">
                <div class="logo-item">
                    <div class="company-logo">TechCorp</div>
                </div>
                <div class="logo-item">
                    <div class="company-logo">InnovateLab</div>
                </div>
                <div class="logo-item">
                    <div class="company-logo">DataFlow</div>
                </div>
                <div class="logo-item">
                    <div class="company-logo">CloudSync</div>
                </div>
                <div class="logo-item">
                    <div class="company-logo">DevTools</div>
                </div>
                <div class="logo-item">
                    <div class="company-logo">ScaleUp</div>
                </div>
                <div class="logo-item">
                    <div class="company-logo">NextGen</div>
                </div>
                <div class="logo-item">
                    <div class="company-logo">WebFlow</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technology Showcase Section -->
    <section class="tech-showcase">
        <div class="showcase-container">
            <div class="section-header animate-on-scroll">
                <div class="section-badge">Technology</div>
                <h2 class="section-title">Complete development ecosystem</h2>
                <p class="section-description">
                    From concept to deployment, we provide everything you need to build, scale, and maintain modern web applications.
                </p>
            </div>

            <div class="tech-tabs">
                <div class="tech-tab active" data-tech="build">
                    <div class="tech-tab-icon">🚀</div>
                    <h3>Build & Deploy</h3>
                    <p>Rapid development and deployment</p>
                </div>
                <div class="tech-tab" data-tech="scale">
                    <div class="tech-tab-icon">📈</div>
                    <h3>Scale & Grow</h3>
                    <p>Automatic scaling and optimization</p>
                </div>
                <div class="tech-tab" data-tech="monitor">
                    <div class="tech-tab-icon">📊</div>
                    <h3>Monitor & Observe</h3>
                    <p>Real-time monitoring and analytics</p>
                </div>
                <div class="tech-tab" data-tech="secure">
                    <div class="tech-tab-icon">🔒</div>
                    <h3>Secure & Maintain</h3>
                    <p>Enterprise-grade security</p>
                </div>
            </div>

            <div class="tech-content">
                <div class="tech-panel active" id="build-panel">
                    <div class="tech-visual">
                        <div class="code-editor">
                            <div class="editor-header">
                                <div class="editor-tabs">
                                    <div class="editor-tab active">app.js</div>
                                    <div class="editor-tab">package.json</div>
                                    <div class="editor-tab">Dockerfile</div>
                                </div>
                            </div>
                            <div class="editor-content">
                                <div class="code-line"><span class="code-keyword">const</span> <span class="code-variable">express</span> = <span class="code-function">require</span>(<span class="code-string">'express'</span>);</div>
                                <div class="code-line"><span class="code-keyword">const</span> <span class="code-variable">app</span> = <span class="code-function">express</span>();</div>
                                <div class="code-line"></div>
                                <div class="code-line"><span class="code-variable">app</span>.<span class="code-function">get</span>(<span class="code-string">'/'</span>, (<span class="code-variable">req</span>, <span class="code-variable">res</span>) => {</div>
                                <div class="code-line">  <span class="code-variable">res</span>.<span class="code-function">json</span>({ <span class="code-property">message</span>: <span class="code-string">'Hello World!'</span> });</div>
                                <div class="code-line">});</div>
                                <div class="code-line"></div>
                                <div class="code-line"><span class="code-variable">app</span>.<span class="code-function">listen</span>(<span class="code-number">3000</span>);</div>
                            </div>
                        </div>
                        <div class="deployment-arrow">→</div>
                        <div class="deployment-result">
                            <div class="result-header">
                                <div class="status-dot success"></div>
                                <span>Deployed Successfully</span>
                            </div>
                            <div class="result-url">https://your-app.devcraft.app</div>
                            <div class="result-stats">
                                <div class="stat">
                                    <span class="stat-label">Build Time</span>
                                    <span class="stat-value">45s</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-label">Status</span>
                                    <span class="stat-value">Live</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tech-panel" id="scale-panel">
                    <div class="tech-visual">
                        <div class="scaling-dashboard">
                            <h4>Auto-scaling in Action</h4>
                            <div class="scaling-metrics">
                                <div class="metric">
                                    <div class="metric-label">CPU Usage</div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 75%"></div>
                                    </div>
                                    <div class="metric-value">75%</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-label">Memory</div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 60%"></div>
                                    </div>
                                    <div class="metric-value">60%</div>
                                </div>
                                <div class="metric">
                                    <div class="metric-label">Requests/min</div>
                                    <div class="metric-bar">
                                        <div class="metric-fill" style="width: 90%"></div>
                                    </div>
                                    <div class="metric-value">12.5K</div>
                                </div>
                            </div>
                            <div class="scaling-action">
                                <div class="action-indicator">
                                    <div class="pulse-dot"></div>
                                    <span>Scaling up to 3 instances...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tech-panel" id="monitor-panel">
                    <div class="tech-visual">
                        <div class="monitoring-dashboard">
                            <h4>Real-time Monitoring</h4>
                            <div class="monitor-grid">
                                <div class="monitor-card">
                                    <div class="monitor-title">Response Time</div>
                                    <div class="monitor-value">127ms</div>
                                    <div class="monitor-trend up">↗ 5% faster</div>
                                </div>
                                <div class="monitor-card">
                                    <div class="monitor-title">Uptime</div>
                                    <div class="monitor-value">99.9%</div>
                                    <div class="monitor-trend stable">→ Stable</div>
                                </div>
                                <div class="monitor-card">
                                    <div class="monitor-title">Error Rate</div>
                                    <div class="monitor-value">0.02%</div>
                                    <div class="monitor-trend down">↘ 12% lower</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tech-panel" id="secure-panel">
                    <div class="tech-visual">
                        <div class="security-dashboard">
                            <h4>Security & Compliance</h4>
                            <div class="security-features">
                                <div class="security-item">
                                    <div class="security-icon">🛡️</div>
                                    <div class="security-info">
                                        <div class="security-title">SSL/TLS Encryption</div>
                                        <div class="security-status">Active</div>
                                    </div>
                                </div>
                                <div class="security-item">
                                    <div class="security-icon">🔐</div>
                                    <div class="security-info">
                                        <div class="security-title">DDoS Protection</div>
                                        <div class="security-status">Enabled</div>
                                    </div>
                                </div>
                                <div class="security-item">
                                    <div class="security-icon">📋</div>
                                    <div class="security-info">
                                        <div class="security-title">SOC 2 Compliance</div>
                                        <div class="security-status">Certified</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="services">
        <div class="features-container">
            <div class="section-header animate-on-scroll">
                <div class="section-badge">Services</div>
                <h2 class="section-title">Everything you need to build modern web applications</h2>
                <p class="section-description">
                    From initial concept to production deployment, we provide comprehensive web development solutions that scale with your business.
                </p>
            </div>

            <div class="features-grid">
                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">🚀</div>
                    <h3 class="feature-title">Build and Deploy</h3>
                    <p class="feature-description">
                        Custom web applications built with modern frameworks and deployed with CI/CD pipelines for rapid iteration and reliable delivery.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">React</span>
                        <span class="feature-tag">Next.js</span>
                        <span class="feature-tag">Node.js</span>
                        <span class="feature-tag">Docker</span>
                    </div>
                </div>

                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">🌐</div>
                    <h3 class="feature-title">Network and Connect</h3>
                    <p class="feature-description">
                        Seamless API integrations, database connections, and third-party service implementations with robust security and authentication.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">REST APIs</span>
                        <span class="feature-tag">GraphQL</span>
                        <span class="feature-tag">OAuth</span>
                        <span class="feature-tag">JWT</span>
                    </div>
                </div>

                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">📈</div>
                    <h3 class="feature-title">Scale and Grow</h3>
                    <p class="feature-description">
                        Performance-optimized applications with auto-scaling infrastructure, CDN integration, and database optimization for high-traffic demands.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">Auto-scaling</span>
                        <span class="feature-tag">CDN</span>
                        <span class="feature-tag">Caching</span>
                        <span class="feature-tag">Load Balancing</span>
                    </div>
                </div>

                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">📊</div>
                    <h3 class="feature-title">Monitor and Observe</h3>
                    <p class="feature-description">
                        Comprehensive monitoring, logging, and analytics implementation to track performance, user behavior, and system health in real-time.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">Analytics</span>
                        <span class="feature-tag">Monitoring</span>
                        <span class="feature-tag">Logging</span>
                        <span class="feature-tag">Alerts</span>
                    </div>
                </div>

                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">🎨</div>
                    <h3 class="feature-title">Design and Experience</h3>
                    <p class="feature-description">
                        User-centered design with responsive layouts, accessibility compliance, and intuitive interfaces that drive engagement and conversions.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">UI/UX</span>
                        <span class="feature-tag">Responsive</span>
                        <span class="feature-tag">Accessibility</span>
                        <span class="feature-tag">Mobile-First</span>
                    </div>
                </div>

                <div class="feature-card animate-on-scroll">
                    <div class="feature-icon">🔧</div>
                    <h3 class="feature-title">Maintain and Support</h3>
                    <p class="feature-description">
                        Ongoing maintenance, security updates, feature enhancements, and technical support to ensure your application stays current and secure.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">24/7 Support</span>
                        <span class="feature-tag">Security</span>
                        <span class="feature-tag">Updates</span>
                        <span class="feature-tag">Backup</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Proof -->
    <section class="social-proof">
        <div class="social-container">
            <div class="section-header animate-on-scroll">
                <h2 class="section-title">Trusted by developers and businesses worldwide</h2>
                <p class="section-description">
                    We've helped companies of all sizes build and scale their web presence with reliable, high-performance solutions.
                </p>
            </div>

            <div class="stats-grid">
                <div class="stat-item animate-on-scroll">
                    <div class="stat-number">150+</div>
                    <div class="stat-label">Projects Delivered</div>
                </div>
                <div class="stat-item animate-on-scroll">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Happy Clients</div>
                </div>
                <div class="stat-item animate-on-scroll">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">Uptime</div>
                </div>
                <div class="stat-item animate-on-scroll">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Support</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Railway-style Interactive Dashboard Section -->
    <section class="dashboard-demo">
        <div class="dashboard-container">
            <div class="section-header animate-on-scroll">
                <div class="section-badge">Experience</div>
                <h2 class="section-title">See our development process in action</h2>
                <p class="section-description">
                    Watch how we transform ideas into production-ready applications with our streamlined workflow.
                </p>
            </div>

            <div class="dashboard-mockup animate-on-scroll">
                <div class="dashboard-header">
                    <div class="dashboard-tabs">
                        <div class="tab active" data-tab="deploy">Deploy</div>
                        <div class="tab" data-tab="monitor">Monitor</div>
                        <div class="tab" data-tab="scale">Scale</div>
                    </div>
                    <div class="dashboard-status">
                        <div class="status-indicator online"></div>
                        <span>All Systems Operational</span>
                    </div>
                </div>

                <div class="dashboard-content">
                    <div class="tab-content active" id="deploy-content">
                        <div class="deployment-flow">
                            <div class="deploy-step completed">
                                <div class="step-icon">✓</div>
                                <div class="step-info">
                                    <h4>Code Push Detected</h4>
                                    <p>GitHub webhook triggered deployment</p>
                                </div>
                                <div class="step-time">2s ago</div>
                            </div>
                            <div class="deploy-step completed">
                                <div class="step-icon">✓</div>
                                <div class="step-info">
                                    <h4>Build Started</h4>
                                    <p>Installing dependencies and building assets</p>
                                </div>
                                <div class="step-time">45s</div>
                            </div>
                            <div class="deploy-step active">
                                <div class="step-icon loading">⟳</div>
                                <div class="step-info">
                                    <h4>Deploying to Production</h4>
                                    <p>Rolling out to global edge network</p>
                                </div>
                                <div class="step-time">12s</div>
                            </div>
                            <div class="deploy-step">
                                <div class="step-icon">○</div>
                                <div class="step-info">
                                    <h4>Health Check</h4>
                                    <p>Verifying deployment status</p>
                                </div>
                                <div class="step-time">Pending</div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="monitor-content">
                        <div class="metrics-grid">
                            <div class="metric-card">
                                <h4>Response Time</h4>
                                <div class="metric-value">127ms</div>
                                <div class="metric-chart">
                                    <canvas id="response-chart" width="100" height="40"></canvas>
                                </div>
                            </div>
                            <div class="metric-card">
                                <h4>Requests/min</h4>
                                <div class="metric-value">2.4K</div>
                                <div class="metric-chart">
                                    <canvas id="requests-chart" width="100" height="40"></canvas>
                                </div>
                            </div>
                            <div class="metric-card">
                                <h4>Error Rate</h4>
                                <div class="metric-value">0.02%</div>
                                <div class="metric-chart">
                                    <canvas id="error-chart" width="100" height="40"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="scale-content">
                        <div class="scaling-info">
                            <div class="scale-metric">
                                <h4>Current Load</h4>
                                <div class="load-bar">
                                    <div class="load-fill" style="width: 65%"></div>
                                </div>
                                <span>65% CPU Usage</span>
                            </div>
                            <div class="scale-actions">
                                <button class="scale-btn">Auto-scale Enabled</button>
                                <button class="scale-btn secondary">Manual Scale</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="testimonials">
        <div class="testimonials-container">
            <div class="section-header animate-on-scroll">
                <div class="section-badge">Testimonials</div>
                <h2 class="section-title">What our clients say</h2>
            </div>

            <div class="testimonials-grid">
                <div class="testimonial-card animate-on-scroll">
                    <p class="testimonial-text">
                        "DevCraft transformed our outdated website into a modern, fast, and user-friendly platform. The team's expertise in React and performance optimization was exactly what we needed."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar">SM</div>
                        <div class="author-info">
                            <h4>Sarah Mitchell</h4>
                            <p>CEO, TechStart Inc.</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card animate-on-scroll">
                    <p class="testimonial-text">
                        "Working with DevCraft was a game-changer. They delivered our e-commerce platform ahead of schedule and it's been running flawlessly for over a year."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar">MR</div>
                        <div class="author-info">
                            <h4>Michael Rodriguez</h4>
                            <p>Founder, EcoShop</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card animate-on-scroll">
                    <p class="testimonial-text">
                        "The attention to detail and modern development practices used by DevCraft helped us scale from 1K to 100K users without any performance issues."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar">JC</div>
                        <div class="author-info">
                            <h4>Jessica Chen</h4>
                            <p>CTO, DataFlow</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="cta-container">
            <h2 class="cta-title">Ready to build something amazing?</h2>
            <p class="cta-description">
                Let's discuss your project and turn your ideas into reality with our expert web development services.
            </p>
            <div class="hero-buttons">
                <a href="#contact" class="btn btn-primary">
                    <span>Start Your Project</span>
                    <span>→</span>
                </a>
                <a href="#work" class="btn btn-secondary">
                    <span>View Portfolio</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Cursor Glow Effect -->
    <div id="cursor-glow" class="cursor-glow"></div>

    <script>
        // Three.js Railway-style 3D Background
        let scene, camera, renderer, particleSystem, geometryObjects = [];
        let mouse = { x: 0, y: 0 };
        let time = 0;

        function init3D() {
            // Scene setup
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            renderer = new THREE.WebGLRenderer({
                canvas: document.getElementById('three-canvas'),
                alpha: true,
                antialias: true
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

            // Create advanced particle system
            createParticleSystem();

            // Create floating geometric shapes
            createGeometricShapes();

            // Create connection lines
            createConnectionLines();

            camera.position.z = 5;
            camera.position.y = 1;
        }

        function createParticleSystem() {
            const particlesGeometry = new THREE.BufferGeometry();
            const particlesCount = 1200;
            const posArray = new Float32Array(particlesCount * 3);
            const colorsArray = new Float32Array(particlesCount * 3);
            const sizesArray = new Float32Array(particlesCount);

            for (let i = 0; i < particlesCount * 3; i += 3) {
                // Position
                posArray[i] = (Math.random() - 0.5) * 60;
                posArray[i + 1] = (Math.random() - 0.5) * 60;
                posArray[i + 2] = (Math.random() - 0.5) * 60;

                // Colors - violet variations
                const intensity = Math.random() * 0.6 + 0.2;
                colorsArray[i] = 0.54 * intensity;     // R
                colorsArray[i + 1] = 0.17 * intensity; // G
                colorsArray[i + 2] = 0.89 * intensity; // B

                // Sizes
                sizesArray[i / 3] = Math.random() * 0.03 + 0.01;
            }

            particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
            particlesGeometry.setAttribute('color', new THREE.BufferAttribute(colorsArray, 3));
            particlesGeometry.setAttribute('size', new THREE.BufferAttribute(sizesArray, 1));

            const particlesMaterial = new THREE.PointsMaterial({
                size: 0.02,
                vertexColors: true,
                transparent: true,
                opacity: 0.8,
                blending: THREE.AdditiveBlending,
                sizeAttenuation: true
            });

            particleSystem = new THREE.Points(particlesGeometry, particlesMaterial);
            scene.add(particleSystem);
        }

        function createGeometricShapes() {
            const geometries = [
                new THREE.BoxGeometry(0.5, 0.5, 0.5),
                new THREE.SphereGeometry(0.3, 8, 6),
                new THREE.OctahedronGeometry(0.4, 0),
                new THREE.TetrahedronGeometry(0.4, 0),
                new THREE.IcosahedronGeometry(0.3, 0)
            ];

            const material = new THREE.MeshBasicMaterial({
                color: 0x8a2be2,
                wireframe: true,
                transparent: true,
                opacity: 0.4
            });

            for (let i = 0; i < 25; i++) {
                const geometry = geometries[Math.floor(Math.random() * geometries.length)];
                const mesh = new THREE.Mesh(geometry, material);

                mesh.position.x = (Math.random() - 0.5) * 40;
                mesh.position.y = (Math.random() - 0.5) * 40;
                mesh.position.z = (Math.random() - 0.5) * 40;

                mesh.rotation.x = Math.random() * 2 * Math.PI;
                mesh.rotation.y = Math.random() * 2 * Math.PI;
                mesh.rotation.z = Math.random() * 2 * Math.PI;

                // Store original position and random speeds
                mesh.userData = {
                    originalX: mesh.position.x,
                    originalY: mesh.position.y,
                    originalZ: mesh.position.z,
                    speedX: (Math.random() - 0.5) * 0.02,
                    speedY: (Math.random() - 0.5) * 0.02,
                    speedZ: (Math.random() - 0.5) * 0.02,
                    rotSpeedX: (Math.random() - 0.5) * 0.02,
                    rotSpeedY: (Math.random() - 0.5) * 0.02,
                    rotSpeedZ: (Math.random() - 0.5) * 0.02
                };

                geometryObjects.push(mesh);
                scene.add(mesh);
            }
        }

        function createConnectionLines() {
            const linesGeometry = new THREE.BufferGeometry();
            const linesPositions = [];
            const linesColors = [];

            for (let i = 0; i < 150; i++) {
                const x1 = (Math.random() - 0.5) * 50;
                const y1 = (Math.random() - 0.5) * 50;
                const z1 = (Math.random() - 0.5) * 50;

                const x2 = x1 + (Math.random() - 0.5) * 15;
                const y2 = y1 + (Math.random() - 0.5) * 15;
                const z2 = z1 + (Math.random() - 0.5) * 15;

                linesPositions.push(x1, y1, z1, x2, y2, z2);

                const intensity = Math.random() * 0.4 + 0.1;
                linesColors.push(0.54 * intensity, 0.17 * intensity, 0.89 * intensity);
                linesColors.push(0.54 * intensity, 0.17 * intensity, 0.89 * intensity);
            }

            linesGeometry.setAttribute('position', new THREE.Float32BufferAttribute(linesPositions, 3));
            linesGeometry.setAttribute('color', new THREE.Float32BufferAttribute(linesColors, 3));

            const linesMaterial = new THREE.LineBasicMaterial({
                vertexColors: true,
                transparent: true,
                opacity: 0.3,
                blending: THREE.AdditiveBlending
            });

            const lines = new THREE.LineSegments(linesGeometry, linesMaterial);
            scene.add(lines);
        }

        function animate() {
            requestAnimationFrame(animate);
            time += 0.01;

            // Rotate particle system
            if (particleSystem) {
                particleSystem.rotation.x += 0.0005;
                particleSystem.rotation.y += 0.001;

                // Animate particle positions
                const positions = particleSystem.geometry.attributes.position.array;
                for (let i = 0; i < positions.length; i += 3) {
                    positions[i + 1] += Math.sin(time + positions[i] * 0.01) * 0.002;
                }
                particleSystem.geometry.attributes.position.needsUpdate = true;
            }

            // Animate geometric shapes
            geometryObjects.forEach(obj => {
                // Floating motion
                obj.position.x = obj.userData.originalX + Math.sin(time + obj.userData.originalX * 0.01) * 2;
                obj.position.y = obj.userData.originalY + Math.cos(time + obj.userData.originalY * 0.01) * 2;
                obj.position.z = obj.userData.originalZ + Math.sin(time * 0.5 + obj.userData.originalZ * 0.01) * 1;

                // Rotation
                obj.rotation.x += obj.userData.rotSpeedX;
                obj.rotation.y += obj.userData.rotSpeedY;
                obj.rotation.z += obj.userData.rotSpeedZ;
            });

            // Mouse interaction
            camera.position.x += (mouse.x * 0.5 - camera.position.x) * 0.02;
            camera.position.y += (-mouse.y * 0.5 - camera.position.y) * 0.02;
            camera.lookAt(scene.position);

            renderer.render(scene, camera);
        }

        // Dashboard Tab Functionality
        function initDashboardTabs() {
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const targetTab = tab.getAttribute('data-tab');

                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));

                    // Add active class to clicked tab and corresponding content
                    tab.classList.add('active');
                    document.getElementById(targetTab + '-content').classList.add('active');

                    // Initialize charts if monitor tab is selected
                    if (targetTab === 'monitor') {
                        setTimeout(initCharts, 100);
                    }
                });
            });
        }

        // Initialize mini charts
        function initCharts() {
            const charts = ['response-chart', 'requests-chart', 'error-chart'];

            charts.forEach((chartId, index) => {
                const canvas = document.getElementById(chartId);
                if (!canvas) return;

                const ctx = canvas.getContext('2d');
                const width = canvas.width;
                const height = canvas.height;

                // Clear canvas
                ctx.clearRect(0, 0, width, height);

                // Generate sample data
                const dataPoints = 20;
                const data = [];
                for (let i = 0; i < dataPoints; i++) {
                    data.push(Math.random() * 0.8 + 0.1);
                }

                // Draw chart
                ctx.strokeStyle = '#8a2be2';
                ctx.lineWidth = 2;
                ctx.beginPath();

                for (let i = 0; i < data.length; i++) {
                    const x = (i / (data.length - 1)) * width;
                    const y = height - (data[i] * height);

                    if (i === 0) {
                        ctx.moveTo(x, y);
                    } else {
                        ctx.lineTo(x, y);
                    }
                }

                ctx.stroke();

                // Add glow effect
                ctx.shadowColor = '#8a2be2';
                ctx.shadowBlur = 10;
                ctx.stroke();
            });
        }

        // Scroll animations
        function initScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.animate-on-scroll').forEach(el => {
                observer.observe(el);
            });
        }

        // Mouse movement tracking
        document.addEventListener('mousemove', (event) => {
            mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
            mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

            // Update cursor glow position
            const cursorGlow = document.getElementById('cursor-glow');
            if (cursorGlow) {
                cursorGlow.style.left = event.clientX + 'px';
                cursorGlow.style.top = event.clientY + 'px';
            }
        });

        // Window resize handler
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        window.addEventListener('resize', onWindowResize);

        // Development Flow Tabs Functionality
        function initFlowTabs() {
            const flowTabs = document.querySelectorAll('.flow-tab');
            const flowPanels = document.querySelectorAll('.flow-panel');
            const timelineSteps = document.querySelectorAll('.timeline-step');

            flowTabs.forEach((tab, index) => {
                tab.addEventListener('click', () => {
                    const targetFlow = tab.getAttribute('data-flow');

                    // Remove active class from all tabs, panels, and timeline steps
                    flowTabs.forEach(t => t.classList.remove('active'));
                    flowPanels.forEach(panel => panel.classList.remove('active'));
                    timelineSteps.forEach(step => step.classList.remove('active'));

                    // Add active class to clicked tab and corresponding panel
                    tab.classList.add('active');
                    document.getElementById(targetFlow + '-flow').classList.add('active');

                    // Activate corresponding timeline step
                    if (timelineSteps[index]) {
                        timelineSteps[index].classList.add('active');
                    }
                });
            });

            // Auto-cycle through flow tabs
            let currentFlowIndex = 0;
            setInterval(() => {
                currentFlowIndex = (currentFlowIndex + 1) % flowTabs.length;
                flowTabs[currentFlowIndex].click();
            }, 4000);
        }

        // Technology Tabs Functionality
        function initTechTabs() {
            const techTabs = document.querySelectorAll('.tech-tab');
            const techPanels = document.querySelectorAll('.tech-panel');

            techTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const targetTech = tab.getAttribute('data-tech');

                    // Remove active class from all tabs and panels
                    techTabs.forEach(t => t.classList.remove('active'));
                    techPanels.forEach(panel => panel.classList.remove('active'));

                    // Add active class to clicked tab and corresponding panel
                    tab.classList.add('active');
                    document.getElementById(targetTech + '-panel').classList.add('active');
                });
            });
        }

        // Animate metrics on scroll
        function animateMetrics() {
            const metrics = document.querySelectorAll('.metric-fill');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const fill = entry.target;
                        const width = fill.style.width;
                        fill.style.width = '0%';
                        setTimeout(() => {
                            fill.style.width = width;
                        }, 200);
                    }
                });
            }, { threshold: 0.5 });

            metrics.forEach(metric => observer.observe(metric));
        }

        // SDLC Train Animation
        function initTrainAnimation() {
            const stations = document.querySelectorAll('.station');
            const trainCars = document.querySelectorAll('.train-car');
            let currentStation = 0;

            function updateStations() {
                stations.forEach((station, index) => {
                    station.classList.toggle('active', index === currentStation);
                });

                // Update train car statuses
                trainCars.forEach((car, index) => {
                    const statusEl = car.querySelector('.car-status');
                    if (index < currentStation) {
                        statusEl.textContent = 'Completed';
                        car.style.borderColor = '#10b981';
                        car.style.background = 'rgba(16, 185, 129, 0.1)';
                    } else if (index === currentStation) {
                        statusEl.textContent = 'In Progress';
                        car.style.borderColor = '#8a2be2';
                        car.style.background = 'rgba(138, 43, 226, 0.1)';
                    } else {
                        statusEl.textContent = 'Pending';
                        car.style.borderColor = '#333';
                        car.style.background = 'var(--bg-card)';
                    }
                });

                currentStation = (currentStation + 1) % stations.length;
            }

            // Update stations every 3 seconds
            setInterval(updateStations, 3000);
        }

        // Railway Dashboard Navigation
        function initRailwayDashboard() {
            const navItems = document.querySelectorAll('.nav-item');
            const dashboardPanels = document.querySelectorAll('.dashboard-panel');

            navItems.forEach(item => {
                item.addEventListener('click', () => {
                    const targetPanel = item.getAttribute('data-panel');

                    // Remove active class from all nav items and panels
                    navItems.forEach(nav => nav.classList.remove('active'));
                    dashboardPanels.forEach(panel => panel.classList.remove('active'));

                    // Add active class to clicked nav item and corresponding panel
                    item.classList.add('active');
                    document.getElementById(targetPanel + '-panel').classList.add('active');
                });
            });

            // Simulate real-time updates
            setInterval(() => {
                updateMetrics();
                updateActivityFeed();
            }, 5000);
        }

        function updateMetrics() {
            const metricValues = document.querySelectorAll('.metric-value');
            metricValues.forEach(metric => {
                if (metric.textContent.includes('ms')) {
                    const currentValue = parseInt(metric.textContent);
                    const newValue = currentValue + Math.floor(Math.random() * 20 - 10);
                    metric.textContent = Math.max(50, newValue) + 'ms';
                } else if (metric.textContent.includes('%')) {
                    const currentValue = parseFloat(metric.textContent);
                    const newValue = currentValue + (Math.random() * 0.2 - 0.1);
                    metric.textContent = Math.max(95, Math.min(100, newValue)).toFixed(1) + '%';
                }
            });
        }

        function updateActivityFeed() {
            const activities = [
                { type: 'success', title: 'Deployment successful', desc: 'portfolio-website deployed to production' },
                { type: 'building', title: 'Build started', desc: 'e-commerce-app building from main branch' },
                { type: 'success', title: 'Tests passed', desc: 'All 47 tests passed for mobile-app' },
                { type: 'success', title: 'Code review approved', desc: 'PR #123 approved and merged' }
            ];

            const activityList = document.querySelector('.activity-list');
            if (activityList && Math.random() > 0.7) {
                const randomActivity = activities[Math.floor(Math.random() * activities.length)];
                const newActivity = document.createElement('div');
                newActivity.className = 'activity-item';
                newActivity.innerHTML = `
                    <div class="activity-icon ${randomActivity.type}">${randomActivity.type === 'success' ? '✓' : '⚡'}</div>
                    <div class="activity-content">
                        <div class="activity-title">${randomActivity.title}</div>
                        <div class="activity-desc">${randomActivity.desc}</div>
                        <div class="activity-time">Just now</div>
                    </div>
                `;
                activityList.insertBefore(newActivity, activityList.firstChild);

                // Remove old activities to keep list manageable
                if (activityList.children.length > 5) {
                    activityList.removeChild(activityList.lastChild);
                }
            }
        }

        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            init3D();
            animate();
            initDashboardTabs();
            initFlowTabs();
            initTechTabs();
            initTrainAnimation();
            initRailwayDashboard();
            initScrollAnimations();
            animateMetrics();

            // Initialize charts after a delay
            setTimeout(initCharts, 1000);
        });
    </script>
</body>
</html>