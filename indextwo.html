<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DevCraft Studios - Web Development Excellence</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-violet: #8a2be2;
            --dark-violet: #6a1b9a;
            --light-violet: #9d4edd;
            --bg-dark: #0a0a0a;
            --bg-card: #111111;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --border-color: rgba(138, 43, 226, 0.2);
            --gradient-primary: linear-gradient(135deg, #8a2be2 0%, #6a1b9a 100%);
            --shadow-primary: 0 20px 40px rgba(138, 43, 226, 0.3);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-dark);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
            font-feature-settings: 'liga' 1, 'kern' 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Header - Railway Style */
        .header {
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            padding: 16px 0;
            background: rgba(10, 10, 10, 0.95);
            backdrop-filter: blur(20px) saturate(180%);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 32px;
            align-items: center;
        }

        .nav-links a {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s ease;
            letter-spacing: -0.01em;
        }

        .nav-links a:hover {
            color: var(--text-primary);
        }

        .cta-nav {
            background: var(--gradient-primary);
            color: white !important;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s ease;
        }

        .cta-nav:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-primary);
        }

        /* Hero Section - Railway Inspired */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            padding: 120px 0 80px;
        }

        #three-canvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            opacity: 0.6;
        }

        .hero-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            position: relative;
            z-index: 2;
        }

        .hero-content {
            max-width: 800px;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid var(--border-color);
            border-radius: 50px;
            padding: 6px 16px;
            font-size: 14px;
            font-weight: 500;
            color: var(--primary-violet);
            margin-bottom: 24px;
            backdrop-filter: blur(10px);
        }

        .hero-title {
            font-size: clamp(48px, 8vw, 80px);
            font-weight: 800;
            line-height: 1.1;
            margin-bottom: 24px;
            letter-spacing: -0.04em;
            background: linear-gradient(135deg, #ffffff 0%, var(--primary-violet) 50%, #ffffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: 20px;
            font-weight: 400;
            color: var(--text-secondary);
            margin-bottom: 40px;
            max-width: 600px;
            line-height: 1.5;
        }

        .hero-buttons {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-family: inherit;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-primary);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.3);
        }

        /* Features Section - Railway Style */
        .features {
            padding: 120px 0;
            background: linear-gradient(180deg, transparent 0%, rgba(138, 43, 226, 0.02) 100%);
        }

        .features-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .section-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .section-badge {
            display: inline-block;
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid var(--border-color);
            border-radius: 50px;
            padding: 4px 12px;
            font-size: 12px;
            font-weight: 600;
            color: var(--primary-violet);
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .section-title {
            font-size: clamp(32px, 5vw, 48px);
            font-weight: 700;
            margin-bottom: 16px;
            letter-spacing: -0.02em;
        }

        .section-description {
            font-size: 18px;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 32px;
            margin-top: 80px;
        }

        .feature-card {
            background: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 32px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            border-color: var(--border-color);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        }

        .feature-card:hover::before {
            opacity: 1;
        }

        .feature-icon {
            width: 56px;
            height: 56px;
            background: var(--gradient-primary);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 24px;
        }

        .feature-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
            letter-spacing: -0.01em;
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .feature-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 16px;
        }

        .feature-tag {
            background: rgba(138, 43, 226, 0.1);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            padding: 4px 12px;
            font-size: 12px;
            color: var(--primary-violet);
            font-weight: 500;
        }

        /* Social Proof Section */
        .social-proof {
            padding: 120px 0;
            background: var(--bg-card);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .social-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 48px;
            margin-top: 64px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 48px;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            letter-spacing: -0.02em;
        }

        .stat-label {
            color: var(--text-secondary);
            font-weight: 500;
            font-size: 14px;
        }

        /* Testimonials */
        .testimonials {
            padding: 80px 0;
        }

        .testimonials-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 48px;
        }

        .testimonial-card {
            background: var(--bg-card);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 24px;
            transition: all 0.3s ease;
        }

        .testimonial-card:hover {
            border-color: var(--border-color);
            transform: translateY(-2px);
        }

        .testimonial-text {
            color: var(--text-secondary);
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 16px;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .author-avatar {
            width: 32px;
            height: 32px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
        }

        .author-info h4 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
        }

        .author-info p {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* CTA Section */
        .cta-section {
            padding: 120px 0;
            background: linear-gradient(135deg, rgba(138, 43, 226, 0.1) 0%, transparent 100%);
        }

        .cta-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 24px;
            text-align: center;
        }

        .cta-title {
            font-size: clamp(32px, 5vw, 48px);
            font-weight: 700;
            margin-bottom: 24px;
            letter-spacing: -0.02em;
        }

        .cta-description {
            font-size: 18px;
            color: var(--text-secondary);
            margin-bottom: 40px;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero {
                padding: 100px 0 60px;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: stretch;
            }

            .features {
                padding: 80px 0;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 24px;
                margin-top: 48px;
            }

            .feature-card {
                padding: 24px;
            }

            .social-proof {
                padding: 80px 0;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 32px;
            }

            .testimonials-grid {
                grid-template-columns: 1fr;
            }

            .cta-section {
                padding: 80px 0;
            }
        }

        /* Smooth scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Loading animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-on-scroll {
            opacity: 0;
            animation: fadeInUp 0.8s ease forwards;
        }

        /* Three.js adjustments for Railway style */
        .particle-system {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">DevCraft</div>
            <nav>
                <ul class="nav-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#work">Work</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#contact" class="cta-nav">Get Started</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <canvas id="three-canvas"></canvas>
        <div class="hero-container">
            <div class="hero-content">
                <div class="hero-badge">
                    <span>✨</span>
                    <span>Crafting Digital Excellence</span>
                </div>
                <h1 class="hero-title">Build and Deploy Web Experiences</h1>
                <p class="hero-subtitle">
                    We transform your vision into stunning, high-performance web applications using cutting-edge technology and modern development practices.
                </p>
                <div class="hero-buttons">
                    <a href="#services" class="btn btn-primary">
                        <span>Our Services</span>
                        <span>→</span>
                    </a>
                    <a href="#work" class="btn btn-secondary">
                        <span>View Our Work</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="services">
        <div class="features-container">
            <div class="section-header">
                <div class="section-badge">Services</div>
                <h2 class="section-title">Everything you need to build modern web applications</h2>
                <p class="section-description">
                    From initial concept to production deployment, we provide comprehensive web development solutions that scale with your business.
                </p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🚀</div>
                    <h3 class="feature-title">Build and Deploy</h3>
                    <p class="feature-description">
                        Custom web applications built with modern frameworks and deployed with CI/CD pipelines for rapid iteration and reliable delivery.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">React</span>
                        <span class="feature-tag">Next.js</span>
                        <span class="feature-tag">Node.js</span>
                        <span class="feature-tag">Docker</span>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🌐</div>
                    <h3 class="feature-title">Network and Connect</h3>
                    <p class="feature-description">
                        Seamless API integrations, database connections, and third-party service implementations with robust security and authentication.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">REST APIs</span>
                        <span class="feature-tag">GraphQL</span>
                        <span class="feature-tag">OAuth</span>
                        <span class="feature-tag">JWT</span>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📈</div>
                    <h3 class="feature-title">Scale and Grow</h3>
                    <p class="feature-description">
                        Performance-optimized applications with auto-scaling infrastructure, CDN integration, and database optimization for high-traffic demands.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">Auto-scaling</span>
                        <span class="feature-tag">CDN</span>
                        <span class="feature-tag">Caching</span>
                        <span class="feature-tag">Load Balancing</span>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3 class="feature-title">Monitor and Observe</h3>
                    <p class="feature-description">
                        Comprehensive monitoring, logging, and analytics implementation to track performance, user behavior, and system health in real-time.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">Analytics</span>
                        <span class="feature-tag">Monitoring</span>
                        <span class="feature-tag">Logging</span>
                        <span class="feature-tag">Alerts</span>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <h3 class="feature-title">Design and Experience</h3>
                    <p class="feature-description">
                        User-centered design with responsive layouts, accessibility compliance, and intuitive interfaces that drive engagement and conversions.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">UI/UX</span>
                        <span class="feature-tag">Responsive</span>
                        <span class="feature-tag">Accessibility</span>
                        <span class="feature-tag">Mobile-First</span>
                    </div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <h3 class="feature-title">Maintain and Support</h3>
                    <p class="feature-description">
                        Ongoing maintenance, security updates, feature enhancements, and technical support to ensure your application stays current and secure.
                    </p>
                    <div class="feature-tags">
                        <span class="feature-tag">24/7 Support</span>
                        <span class="feature-tag">Security</span>
                        <span class="feature-tag">Updates</span>
                        <span class="feature-tag">Backup</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Proof -->
    <section class="social-proof">
        <div class="social-container">
            <div class="section-header">
                <h2 class="section-title">Trusted by developers and businesses worldwide</h2>
                <p class="section-description">
                    We've helped companies of all sizes build and scale their web presence with reliable, high-performance solutions.
                </p>
            </div>

            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">150+</div>
                    <div class="stat-label">Projects Delivered</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">Happy Clients</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">99.9%</div>
                    <div class="stat-label">Uptime</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">Support</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials -->
    <section class="testimonials">
        <div class="testimonials-container">
            <div class="section-header">
                <div class="section-badge">Testimonials</div>
                <h2 class="section-title">What our clients say</h2>
            </div>

            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <p class="testimonial-text">
                        "DevCraft transformed our outdated website into a modern, fast, and user-friendly platform. The team's expertise in React and performance optimization was exactly what we needed."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar">SM</div>
                        <div class="author-info">
                            <h4>Sarah Mitchell</h4>
                            <p>CEO, TechStart Inc.</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <p class="testimonial-text">
                        "Working with DevCraft was a game-changer. They delivered our e-commerce platform ahead of schedule and it's been running flawlessly for over a year."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar">MR</div>
                        <div class="author-info">
                            <h4>Michael Rodriguez</h4>
                            <p>Founder, EcoShop</p>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <p class="testimonial-text">
                        "The attention to detail and modern development practices used by DevCraft helped us scale from 1K to 100K users without any performance issues."
                    </p>
                    <div class="testimonial-author">
                        <div class="author-avatar">JC</div>
                        <div class="author-info">
                            <h4>Jessica Chen</h4>
                            <p>CTO, DataFlow</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="cta-container">
            <h2 class="cta-title">Ready to build something amazing?</h2>
            <p class="cta-description">
                Let's discuss your project and turn your ideas into reality with our expert web development services.
            </p>
            <div class="hero-buttons">
                <a href="#contact" class="btn btn-primary">
                    <span>Start Your Project</span>
                    <span>→</span>
                </a>
                <a href="#work" class="btn btn-secondary">
                    <span>View Portfolio</span>
                </a>
            </div>
        </div>
    </section>

    <script>
        // Three.js Railway-style particle system
        let scene, camera, renderer, particleSystem, time = 0;

        function init3D() {
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            renderer = new THREE.WebGLRenderer({ 
                canvas: document.getElementById('three-canvas'), 
                alpha: true,
                antialias: true
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

            // Create subtle particle system
            const particlesGeometry = new THREE.BufferGeometry();
            const particlesCount = 800;
            const posArray = new Float32Array(particlesCount * 3);
            const colorsArray = new Float32Array(particlesCount * 3);

            for (let i = 0; i < particlesCount * 3; i += 3) {
                // Position
                posArray[i] = (Math.random() - 0.5) * 50;
                posArray[i + 1] = (Math.random() - 0.5) * 50;
                posArray[i + 2] = (Math.random() - 0.5) * 50;

                // Colors - violet variations
                const intensity = Math.random() * 0.5 + 0.3;
                colorsArray[i] = 0.54 * intensity;     // R
                colorsArray[i + 1] = 0.17 * intensity; // G
                colorsArray[i + 2] = 0.89 * intensity; // B
            }

            particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));
            particlesGeometry.setAttribute('color', new THREE.BufferAttribute(colorsArray, 3));

            const particlesMaterial = new THREE.PointsMaterial({
                size: 0.02,
                vertexColors: true,
                transparent: true,
                opacity: 0.6,
                blending: THREE.AdditiveBlending
            });

            particleSystem = new THREE.Points(particlesGeometry, particlesMaterial);
            scene.add(particleSystem);

            // Create connection lines
            const linesGeometry = new THREE.BufferGeometry();
            const linesPositions = [];
            const linesColors = [];

            for (let i = 0; i < 100; i++) {
                const x1 = (Math.random() - 0.5) * 30;
                const y1 = (Math.random() - 0.5) * 30;
                const z1 = (Math.random() - 0.5) * 30;
                
                const x2 = x1 + (Math.random() - 0.5) * 10;
                const y2 = y1 + (Math.random() - 0.5) * 10;
                const z2 = z1 + (Math.random() - 0.5) * 10;

                linesPositions.push(x1, y1, z1, x2, y2, z2);
                
                const intensity = Math.random() * 0.3 + 0.1;
                linesColors.push(0.54 * intensity, 0.17 * intensity, 0.89 * intensity);
                linesColors.push(0.54 * intensity, 0.17 * intensity, 0.89 * intensity);
            }

            linesGeometry.setAttribute('position', new THREE.Float32BufferAttribute(linesPositions,